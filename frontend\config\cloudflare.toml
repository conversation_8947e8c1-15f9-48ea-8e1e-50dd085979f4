# Cloudflare Pages Configuration for Reality 2.0 Frontend
# https://developers.cloudflare.com/pages/platform/build-configuration/

name = "reality2-frontend"
compatibility_date = "2024-01-01"

[build]
command = "npm run build:cdn"
destination = "dist"

[build.environment_variables]
NODE_VERSION = "18"
VITE_CDN_ENABLED = "true"
VITE_CDN_URL = "https://cdn.reality2.app"

# Production environment
[env.production]
VITE_APP_ENVIRONMENT = "production"
VITE_API_BASE_URL = "https://api.reality2.app"

# Staging environment
[env.staging]
VITE_APP_ENVIRONMENT = "staging"
VITE_API_BASE_URL = "https://staging-api.reality2.app"

# Custom headers for optimal caching
[[headers]]
for = "/*"
[headers.values]
X-Frame-Options = "DENY"
X-Content-Type-Options = "nosniff"
Referrer-Policy = "strict-origin-when-cross-origin"
Permissions-Policy = "camera=(), microphone=(), geolocation=()"

[[headers]]
for = "/*.html"
[headers.values]
Cache-Control = "public, max-age=3600, s-maxage=3600"

[[headers]]
for = "/css/*"
[headers.values]
Cache-Control = "public, max-age=********, immutable"

[[headers]]
for = "/js/*"
[headers.values]
Cache-Control = "public, max-age=********, immutable"

[[headers]]
for = "/images/*"
[headers.values]
Cache-Control = "public, max-age=********, immutable"

[[headers]]
for = "/fonts/*"
[headers.values]
Cache-Control = "public, max-age=********, immutable"
Access-Control-Allow-Origin = "*"

# Redirects for SPA routing
[[redirects]]
from = "/*"
to = "/index.html"
status = 200

# Security redirects
[[redirects]]
from = "http://reality2.app/*"
to = "https://reality2.app/:splat"
status = 301
force = true

[[redirects]]
from = "http://www.reality2.app/*"
to = "https://reality2.app/:splat"
status = 301
force = true
