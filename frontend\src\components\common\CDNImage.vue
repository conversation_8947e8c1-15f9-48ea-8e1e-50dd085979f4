<template>
  <div 
    :class="[
      'cdn-image-container',
      containerClass,
      {
        'loading': loading,
        'error': error,
        'loaded': !loading && !error && src
      }
    ]"
    :style="containerStyle"
  >
    <!-- Loading placeholder -->
    <div 
      v-if="loading && showPlaceholder" 
      class="cdn-image-placeholder"
      :style="placeholderStyle"
    >
      <div class="cdn-image-spinner" v-if="showSpinner">
        <svg class="animate-spin h-6 w-6" fill="none" viewBox="0 0 24 24">
          <circle 
            class="opacity-25" 
            cx="12" 
            cy="12" 
            r="10" 
            stroke="currentColor" 
            stroke-width="4"
          />
          <path 
            class="opacity-75" 
            fill="currentColor" 
            d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"
          />
        </svg>
      </div>
      <slot name="placeholder" v-else>
        <div class="cdn-image-placeholder-text">Loading...</div>
      </slot>
    </div>
    
    <!-- Error state -->
    <div 
      v-else-if="error && showError" 
      class="cdn-image-error"
      :style="placeholderStyle"
    >
      <slot name="error" :error="error" :retry="reload">
        <div class="cdn-image-error-content">
          <svg class="h-8 w-8 text-gray-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path 
              stroke-linecap="round" 
              stroke-linejoin="round" 
              stroke-width="2" 
              d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L4.082 16.5c-.77.833.192 2.5 1.732 2.5z" 
            />
          </svg>
          <p class="text-sm text-gray-500 mt-2">Failed to load image</p>
          <button 
            @click="reload" 
            class="text-xs text-blue-500 hover:text-blue-700 mt-1"
          >
            Retry
          </button>
        </div>
      </slot>
    </div>
    
    <!-- Responsive image -->
    <picture v-else-if="responsive && srcSet">
      <source 
        v-for="(source, index) in sources" 
        :key="index"
        :srcset="source.srcset"
        :media="source.media"
        :type="source.type"
      />
      <img
        :src="src"
        :srcset="srcSet"
        :alt="alt"
        :class="imageClass"
        :style="imageStyle"
        :loading="lazy ? 'lazy' : 'eager'"
        :decoding="async ? 'async' : 'sync'"
        @load="onLoad"
        @error="onError"
        @click="$emit('click', $event)"
      />
    </picture>
    
    <!-- Standard image -->
    <img
      v-else-if="src"
      :src="src"
      :alt="alt"
      :class="imageClass"
      :style="imageStyle"
      :loading="lazy ? 'lazy' : 'eager'"
      :decoding="async ? 'async' : 'sync'"
      @load="onLoad"
      @error="onError"
      @click="$emit('click', $event)"
    />
  </div>
</template>

<script setup lang="ts">
import { computed, type CSSProperties } from 'vue'
import { useImage, useResponsiveImage } from '@/composables/useCDN'
import type { ImageOptimizationOptions } from '@/services/cdnService'

export interface CDNImageProps {
  src: string
  alt: string
  width?: number
  height?: number
  quality?: number
  format?: 'webp' | 'avif' | 'jpg' | 'png'
  lazy?: boolean
  async?: boolean
  responsive?: boolean
  widths?: number[]
  sizes?: string
  fallback?: string
  placeholder?: string
  showPlaceholder?: boolean
  showSpinner?: boolean
  showError?: boolean
  containerClass?: string
  imageClass?: string
  objectFit?: 'contain' | 'cover' | 'fill' | 'none' | 'scale-down'
  objectPosition?: string
}

const props = withDefaults(defineProps<CDNImageProps>(), {
  quality: 85,
  format: 'webp',
  lazy: true,
  async: true,
  responsive: false,
  widths: () => [320, 640, 768, 1024, 1280, 1920],
  sizes: '100vw',
  showPlaceholder: true,
  showSpinner: true,
  showError: true,
  objectFit: 'cover',
  objectPosition: 'center'
})

const emit = defineEmits<{
  click: [event: MouseEvent]
  load: [event: Event]
  error: [error: Error]
}>()

// Image optimization options
const imageOptions = computed<ImageOptimizationOptions>(() => ({
  width: props.width,
  height: props.height,
  quality: props.quality,
  format: props.format,
  lazy: props.lazy
}))

// Use appropriate composable based on responsive setting
const imageComposable = props.responsive 
  ? useResponsiveImage(props.src, props.widths, imageOptions.value)
  : useImage(props.src, imageOptions.value)

const { src, loading, error, load, reload } = imageComposable
const srcSet = 'srcSet' in imageComposable ? imageComposable.srcSet : computed(() => '')

// Generate responsive sources for different formats
const sources = computed(() => {
  if (!props.responsive || !srcSet.value) return []
  
  const formats = ['avif', 'webp']
  return formats.map(format => ({
    srcset: srcSet.value.replace(/\.(jpg|jpeg|png|webp|avif)/g, `.${format}`),
    type: `image/${format}`,
    media: props.sizes
  }))
})

// Computed styles
const containerStyle = computed<CSSProperties>(() => ({
  width: props.width ? `${props.width}px` : undefined,
  height: props.height ? `${props.height}px` : undefined,
  aspectRatio: props.width && props.height ? `${props.width} / ${props.height}` : undefined
}))

const placeholderStyle = computed<CSSProperties>(() => ({
  width: '100%',
  height: '100%',
  backgroundColor: '#f3f4f6',
  display: 'flex',
  alignItems: 'center',
  justifyContent: 'center',
  minHeight: props.height ? `${props.height}px` : '200px'
}))

const imageStyle = computed<CSSProperties>(() => ({
  width: '100%',
  height: '100%',
  objectFit: props.objectFit,
  objectPosition: props.objectPosition
}))

// Event handlers
const onLoad = (event: Event) => {
  emit('load', event)
}

const onError = (event: Event) => {
  const error = new Error('Image failed to load')
  emit('error', error)
}
</script>

<style scoped>
.cdn-image-container {
  @apply relative overflow-hidden;
}

.cdn-image-container.loading {
  @apply bg-gray-100;
}

.cdn-image-container.error {
  @apply bg-gray-50;
}

.cdn-image-placeholder {
  @apply absolute inset-0 flex items-center justify-center;
}

.cdn-image-spinner {
  @apply text-gray-400;
}

.cdn-image-placeholder-text {
  @apply text-sm text-gray-500;
}

.cdn-image-error {
  @apply absolute inset-0 flex items-center justify-center;
}

.cdn-image-error-content {
  @apply text-center;
}

.cdn-image-container img {
  @apply transition-opacity duration-300;
}

.cdn-image-container.loading img {
  @apply opacity-0;
}

.cdn-image-container.loaded img {
  @apply opacity-100;
}

/* Responsive image optimization */
@media (max-width: 640px) {
  .cdn-image-container {
    @apply max-w-full;
  }
}

/* High DPI display optimization */
@media (-webkit-min-device-pixel-ratio: 2), (min-resolution: 192dpi) {
  .cdn-image-container img {
    image-rendering: -webkit-optimize-contrast;
    image-rendering: crisp-edges;
  }
}

/* Print optimization */
@media print {
  .cdn-image-placeholder,
  .cdn-image-error {
    @apply hidden;
  }
  
  .cdn-image-container img {
    @apply opacity-100;
  }
}
</style>
