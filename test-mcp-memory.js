#!/usr/bin/env node

/**
 * Reality 2.0 - MCP Memory Server Test Script
 * Tests the functionality of the MCP memory server
 */

const { spawn } = require('child_process');
const path = require('path');
const fs = require('fs');

// Configuration
const PROJECT_DIR = __dirname;
const MEMORY_DATA_DIR = path.join(PROJECT_DIR, 'mcp-memory-data');
const TEST_LOG = path.join(PROJECT_DIR, 'mcp-memory-test.log');

// Colors for console output
const colors = {
    reset: '\x1b[0m',
    bright: '\x1b[1m',
    red: '\x1b[31m',
    green: '\x1b[32m',
    yellow: '\x1b[33m',
    blue: '\x1b[34m',
    magenta: '\x1b[35m',
    cyan: '\x1b[36m'
};

// Helper functions
function log(message, color = 'reset') {
    const timestamp = new Date().toISOString();
    const colorCode = colors[color] || colors.reset;
    console.log(`${colorCode}[${timestamp}] ${message}${colors.reset}`);
    
    // Also log to file
    fs.appendFileSync(TEST_LOG, `[${timestamp}] ${message}\n`);
}

function logSuccess(message) {
    log(`✅ ${message}`, 'green');
}

function logError(message) {
    log(`❌ ${message}`, 'red');
}

function logWarning(message) {
    log(`⚠️  ${message}`, 'yellow');
}

function logInfo(message) {
    log(`ℹ️  ${message}`, 'blue');
}

// Test functions
async function checkNodejs() {
    return new Promise((resolve) => {
        const nodeProcess = spawn('node', ['--version']);
        
        nodeProcess.on('close', (code) => {
            if (code === 0) {
                logSuccess('Node.js is available');
                resolve(true);
            } else {
                logError('Node.js is not available');
                resolve(false);
            }
        });
        
        nodeProcess.on('error', () => {
            logError('Node.js is not installed');
            resolve(false);
        });
    });
}

async function checkNpx() {
    return new Promise((resolve) => {
        const npxProcess = spawn('npx', ['--version']);
        
        npxProcess.on('close', (code) => {
            if (code === 0) {
                logSuccess('npx is available');
                resolve(true);
            } else {
                logError('npx is not available');
                resolve(false);
            }
        });
        
        npxProcess.on('error', () => {
            logError('npx is not installed');
            resolve(false);
        });
    });
}

async function checkMcpServer() {
    return new Promise((resolve) => {
        logInfo('Checking MCP memory server...');
        
        const mcpProcess = spawn('npx', ['-y', '@modelcontextprotocol/server-memory', '--version']);
        
        let output = '';
        mcpProcess.stdout.on('data', (data) => {
            output += data.toString();
        });
        
        mcpProcess.stderr.on('data', (data) => {
            output += data.toString();
        });
        
        mcpProcess.on('close', (code) => {
            if (code === 0) {
                logSuccess('MCP memory server is available');
                logInfo(`Server version info: ${output.trim()}`);
                resolve(true);
            } else {
                logWarning('MCP memory server installation may be needed');
                logInfo(`Output: ${output.trim()}`);
                resolve(false);
            }
        });
        
        mcpProcess.on('error', (error) => {
            logError(`Error checking MCP server: ${error.message}`);
            resolve(false);
        });
    });
}

async function testServerStart() {
    return new Promise((resolve) => {
        logInfo('Testing MCP memory server startup...');
        
        const mcpProcess = spawn('npx', ['-y', '@modelcontextprotocol/server-memory'], {
            env: {
                ...process.env,
                MEMORY_STORAGE_PATH: MEMORY_DATA_DIR
            }
        });
        
        let output = '';
        let errorOutput = '';
        
        mcpProcess.stdout.on('data', (data) => {
            output += data.toString();
        });
        
        mcpProcess.stderr.on('data', (data) => {
            errorOutput += data.toString();
        });
        
        // Give the server a few seconds to start
        setTimeout(() => {
            mcpProcess.kill('SIGTERM');
        }, 5000);
        
        mcpProcess.on('close', (code) => {
            if (code === 0 || code === null) {
                logSuccess('MCP memory server started and stopped successfully');
                if (output) {
                    logInfo(`Server output: ${output.trim()}`);
                }
                resolve(true);
            } else {
                logError(`MCP memory server failed to start (exit code: ${code})`);
                if (errorOutput) {
                    logError(`Error output: ${errorOutput.trim()}`);
                }
                resolve(false);
            }
        });
        
        mcpProcess.on('error', (error) => {
            logError(`Error starting MCP server: ${error.message}`);
            resolve(false);
        });
    });
}

function checkDirectories() {
    logInfo('Checking directories...');
    
    // Check if memory data directory exists
    if (fs.existsSync(MEMORY_DATA_DIR)) {
        logSuccess(`Memory data directory exists: ${MEMORY_DATA_DIR}`);
    } else {
        logInfo('Creating memory data directory...');
        try {
            fs.mkdirSync(MEMORY_DATA_DIR, { recursive: true });
            logSuccess(`Memory data directory created: ${MEMORY_DATA_DIR}`);
        } catch (error) {
            logError(`Failed to create memory data directory: ${error.message}`);
            return false;
        }
    }
    
    return true;
}

function generateReport(results) {
    logInfo('Generating test report...');
    
    const report = {
        timestamp: new Date().toISOString(),
        projectDir: PROJECT_DIR,
        memoryDataDir: MEMORY_DATA_DIR,
        testResults: results,
        summary: {
            total: Object.keys(results).length,
            passed: Object.values(results).filter(r => r).length,
            failed: Object.values(results).filter(r => !r).length
        }
    };
    
    const reportFile = path.join(PROJECT_DIR, 'mcp-memory-test-report.json');
    fs.writeFileSync(reportFile, JSON.stringify(report, null, 2));
    
    logInfo(`Test report saved: ${reportFile}`);
    
    // Print summary
    console.log('\n' + '='.repeat(50));
    log('🧪 MCP Memory Server Test Summary', 'cyan');
    console.log('='.repeat(50));
    
    Object.entries(results).forEach(([test, passed]) => {
        const status = passed ? '✅ PASS' : '❌ FAIL';
        const color = passed ? 'green' : 'red';
        log(`${status} ${test}`, color);
    });
    
    console.log('='.repeat(50));
    log(`Total: ${report.summary.total}, Passed: ${report.summary.passed}, Failed: ${report.summary.failed}`, 'cyan');
    
    if (report.summary.failed === 0) {
        logSuccess('All tests passed! MCP memory server is ready to use.');
    } else {
        logWarning('Some tests failed. Check the setup guide for troubleshooting.');
    }
}

// Main test function
async function runTests() {
    log('🧠 Reality 2.0 - MCP Memory Server Test Suite', 'cyan');
    console.log('='.repeat(50));
    
    const results = {};
    
    // Test Node.js availability
    results['Node.js Available'] = await checkNodejs();
    
    // Test npx availability
    results['npx Available'] = await checkNpx();
    
    // Check directories
    results['Directories Setup'] = checkDirectories();
    
    // Test MCP server availability
    results['MCP Server Available'] = await checkMcpServer();
    
    // Test server startup (only if previous tests passed)
    if (results['Node.js Available'] && results['npx Available'] && results['MCP Server Available']) {
        results['Server Startup Test'] = await testServerStart();
    } else {
        logWarning('Skipping server startup test due to previous failures');
        results['Server Startup Test'] = false;
    }
    
    // Generate report
    generateReport(results);
    
    return results;
}

// Run tests if this script is executed directly
if (require.main === module) {
    runTests().catch((error) => {
        logError(`Test suite failed: ${error.message}`);
        process.exit(1);
    });
}

module.exports = { runTests };
