[2025-06-01T11:58:27.374Z] 🧠 Reality 2.0 - MCP Memory Server Test Suite
[2025-06-01T11:58:27.530Z] ✅ Node.js is available
[2025-06-01T11:58:27.541Z] ❌ npx is not installed
[2025-06-01T11:58:27.544Z] ℹ️  Checking directories...
[2025-06-01T11:58:27.546Z] ℹ️  Creating memory data directory...
[2025-06-01T11:58:27.549Z] ✅ Memory data directory created: C:\Users\<USER>\Documents\augment-projects\Reality-2.0\mcp-memory-data
[2025-06-01T11:58:27.551Z] ℹ️  Checking MCP memory server...
[2025-06-01T11:58:27.558Z] ❌ Error checking MCP server: spawn npx ENOENT
[2025-06-01T11:58:27.560Z] ⚠️  Skipping server startup test due to previous failures
[2025-06-01T11:58:27.562Z] ℹ️  Generating test report...
[2025-06-01T11:58:27.565Z] ℹ️  Test report saved: C:\Users\<USER>\Documents\augment-projects\Reality-2.0\mcp-memory-test-report.json
[2025-06-01T11:58:27.567Z] 🧪 MCP Memory Server Test Summary
[2025-06-01T11:58:27.569Z] ✅ PASS Node.js Available
[2025-06-01T11:58:27.570Z] ❌ FAIL npx Available
[2025-06-01T11:58:27.572Z] ✅ PASS Directories Setup
[2025-06-01T11:58:27.573Z] ❌ FAIL MCP Server Available
[2025-06-01T11:58:27.575Z] ❌ FAIL Server Startup Test
[2025-06-01T11:58:27.577Z] Total: 5, Passed: 2, Failed: 3
[2025-06-01T11:58:27.579Z] ⚠️  Some tests failed. Check the setup guide for troubleshooting.
[2025-06-01T11:58:27.585Z] ⚠️  MCP memory server installation may be needed
[2025-06-01T11:58:27.587Z] ℹ️  Output: 
[2025-06-01T11:58:27.589Z] ❌ npx is not available
