#!/bin/bash

# Reality 2.0 - Containerization Test Script
# Tests the complete Docker setup for both development and production

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

print_header() {
    echo -e "${BLUE}🧪 Reality 2.0 - Containerization Test Suite${NC}"
    echo -e "${BLUE}=============================================${NC}"
}

print_success() {
    echo -e "${GREEN}✅ $1${NC}"
}

print_warning() {
    echo -e "${YELLOW}⚠️  $1${NC}"
}

print_error() {
    echo -e "${RED}❌ $1${NC}"
}

print_info() {
    echo -e "${BLUE}ℹ️  $1${NC}"
}

# Test Docker availability
test_docker() {
    print_info "Testing Docker availability..."
    if docker --version > /dev/null 2>&1; then
        print_success "Docker is available: $(docker --version)"
    else
        print_error "Docker is not available"
        exit 1
    fi
    
    if docker-compose --version > /dev/null 2>&1; then
        print_success "Docker Compose is available: $(docker-compose --version)"
    else
        print_error "Docker Compose is not available"
        exit 1
    fi
}

# Test Dockerfile syntax
test_dockerfiles() {
    print_info "Testing Dockerfile syntax..."
    
    # Test backend Dockerfile
    if docker build -f Dockerfile.backend --target development -t test-backend . --dry-run > /dev/null 2>&1; then
        print_success "Backend Dockerfile syntax is valid"
    else
        print_warning "Backend Dockerfile syntax check failed (dry-run not supported)"
    fi
    
    # Test frontend Dockerfile
    if docker build -f Dockerfile.frontend --target development -t test-frontend . --dry-run > /dev/null 2>&1; then
        print_success "Frontend Dockerfile syntax is valid"
    else
        print_warning "Frontend Dockerfile syntax check failed (dry-run not supported)"
    fi
}

# Test docker-compose configuration
test_compose_config() {
    print_info "Testing docker-compose configurations..."
    
    # Test development compose
    if docker-compose -f docker-compose.reality.yml config > /dev/null 2>&1; then
        print_success "Development docker-compose configuration is valid"
    else
        print_error "Development docker-compose configuration is invalid"
        return 1
    fi
    
    # Test production compose
    if docker-compose -f docker-compose.prod.yml config > /dev/null 2>&1; then
        print_success "Production docker-compose configuration is valid"
    else
        print_error "Production docker-compose configuration is invalid"
        return 1
    fi
}

# Test image building
test_image_building() {
    print_info "Testing Docker image building..."
    
    # Build backend development image
    print_info "Building backend development image..."
    if docker build -f Dockerfile.backend --target development -t reality2-backend:test . > /dev/null 2>&1; then
        print_success "Backend development image built successfully"
    else
        print_error "Failed to build backend development image"
        return 1
    fi
    
    # Build frontend development image
    print_info "Building frontend development image..."
    if docker build -f Dockerfile.frontend --target development -t reality2-frontend:test . > /dev/null 2>&1; then
        print_success "Frontend development image built successfully"
    else
        print_error "Failed to build frontend development image"
        return 1
    fi
}

# Test environment files
test_environment_files() {
    print_info "Testing environment files..."
    
    if [ -f ".env.docker" ]; then
        print_success "Docker environment file exists"
    else
        print_warning "Docker environment file (.env.docker) not found"
    fi
    
    if [ -f ".dockerignore" ]; then
        print_success "Docker ignore file exists"
    else
        print_warning "Docker ignore file (.dockerignore) not found"
    fi
}

# Test management scripts
test_management_scripts() {
    print_info "Testing management scripts..."
    
    if [ -f "docker-manage.sh" ] && [ -x "docker-manage.sh" ]; then
        print_success "Linux management script exists and is executable"
    else
        print_warning "Linux management script (docker-manage.sh) not found or not executable"
    fi
    
    if [ -f "docker-manage.bat" ]; then
        print_success "Windows management script exists"
    else
        print_warning "Windows management script (docker-manage.bat) not found"
    fi
}

# Test container startup (quick test)
test_container_startup() {
    print_info "Testing container startup (quick test)..."
    
    # Start containers in detached mode
    if docker-compose -f docker-compose.reality.yml --env-file .env.docker up -d > /dev/null 2>&1; then
        print_success "Containers started successfully"
        
        # Wait a moment for containers to initialize
        sleep 10
        
        # Check container status
        if docker-compose -f docker-compose.reality.yml ps | grep -q "Up"; then
            print_success "Containers are running"
        else
            print_warning "Containers may not be fully ready"
        fi
        
        # Stop containers
        docker-compose -f docker-compose.reality.yml down > /dev/null 2>&1
        print_success "Containers stopped successfully"
    else
        print_error "Failed to start containers"
        return 1
    fi
}

# Cleanup test images
cleanup_test_images() {
    print_info "Cleaning up test images..."
    docker rmi reality2-backend:test reality2-frontend:test > /dev/null 2>&1 || true
    print_success "Test images cleaned up"
}

# Main test function
run_tests() {
    print_header
    echo
    
    test_docker
    echo
    
    test_dockerfiles
    echo
    
    test_compose_config
    echo
    
    test_environment_files
    echo
    
    test_management_scripts
    echo
    
    test_image_building
    echo
    
    test_container_startup
    echo
    
    cleanup_test_images
    echo
    
    print_header
    print_success "All containerization tests completed!"
    echo
    print_info "Next steps:"
    echo "  1. Start development environment: ./docker-manage.sh dev-start-bg"
    echo "  2. Access frontend: http://localhost:3000"
    echo "  3. Access backend API: http://localhost:8000"
    echo "  4. View API docs: http://localhost:8000/docs"
    echo "  5. Stop environment: ./docker-manage.sh dev-stop"
}

# Run all tests
run_tests
