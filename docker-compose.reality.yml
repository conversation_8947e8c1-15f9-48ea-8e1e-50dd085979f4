version: '3.8'

services:
  # FastAPI Backend for Reality 2.0
  backend:
    build:
      context: .
      dockerfile: Dockerfile.backend
      target: development
    container_name: reality-backend
    environment:
      - BACKEND_HOST=${BACKEND_HOST:-0.0.0.0}
      - BACKEND_PORT=${BACKEND_PORT:-8000}
      - CORS_ORIGINS=${CORS_ORIGINS:-http://localhost:3000,http://127.0.0.1:3000}
      - APP_ENVIRONMENT=${APP_ENVIRONMENT:-development}
      - MAPY_CZ_API_KEY=${MAPY_CZ_API_KEY:-8Rtq998py5jq8tJQ-HjbX17FiM7AnjwQxQzljSHmWJE}
    ports:
      - "${BACKEND_PORT:-8000}:8000"
    volumes:
      - ./backend:/app
      - ./backend/logs:/app/logs
      - ./backend/uploads:/app/uploads
    networks:
      - reality-network
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8000/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s
    develop:
      watch:
        - action: sync
          path: ./backend
          target: /app
          ignore:
            - __pycache__/
            - "*.pyc"
            - .pytest_cache/
            - logs/
            - uploads/

  # Vue.js Frontend for Reality 2.0
  frontend:
    build:
      context: .
      dockerfile: Dockerfile.frontend
      target: development
    container_name: reality-frontend
    environment:
      - VITE_API_BASE_URL=${VITE_API_BASE_URL:-http://localhost:8000}
      - VITE_APP_NAME=${VITE_APP_NAME:-Reality 2.0}
      - VITE_APP_ENVIRONMENT=${APP_ENVIRONMENT:-development}
    ports:
      - "3000:3000"
    volumes:
      - ./frontend:/app
      - /app/node_modules
    depends_on:
      - backend
    networks:
      - reality-network
    restart: unless-stopped
    develop:
      watch:
        - action: sync
          path: ./frontend/src
          target: /app/src
        - action: rebuild
          path: ./frontend/package.json

networks:
  reality-network:
    driver: bridge
    name: reality-network
