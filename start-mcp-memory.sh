#!/bin/bash

# Reality 2.0 - MCP Memory Server Startup Script
# Starts the Model Context Protocol memory server for persistent memory management

set -e

# Configuration
PROJECT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
MEMORY_DATA_DIR="$PROJECT_DIR/mcp-memory-data"
LOG_FILE="$PROJECT_DIR/mcp-memory.log"

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

print_header() {
    echo -e "${BLUE}🧠 Reality 2.0 - MCP Memory Server${NC}"
    echo -e "${BLUE}==================================${NC}"
    echo
}

print_success() {
    echo -e "${GREEN}✅ $1${NC}"
}

print_warning() {
    echo -e "${YELLOW}⚠️  $1${NC}"
}

print_error() {
    echo -e "${RED}❌ $1${NC}"
}

print_info() {
    echo -e "${BLUE}ℹ️  $1${NC}"
}

# Check if Node.js is available
check_nodejs() {
    if ! command -v node &> /dev/null; then
        print_error "Node.js is not installed or not in PATH"
        echo "Please install Node.js from https://nodejs.org/"
        exit 1
    fi
    
    print_success "Node.js is available: $(node --version)"
}

# Check if npx is available
check_npx() {
    if ! command -v npx &> /dev/null; then
        print_error "npx is not available"
        echo "Please ensure npm is properly installed"
        exit 1
    fi
    
    print_success "npx is available"
}

# Create memory data directory
setup_directories() {
    if [ ! -d "$MEMORY_DATA_DIR" ]; then
        print_info "Creating memory data directory..."
        mkdir -p "$MEMORY_DATA_DIR"
        print_success "Memory data directory created: $MEMORY_DATA_DIR"
    fi
}

# Check if MCP memory server is available
check_mcp_server() {
    print_info "Checking MCP memory server availability..."
    
    if ! npx -y @modelcontextprotocol/server-memory --version &> /dev/null; then
        print_warning "MCP memory server not found, installing..."
        print_info "Installing @modelcontextprotocol/server-memory..."
        
        if ! npx -y @modelcontextprotocol/server-memory --help &> /dev/null; then
            print_error "Failed to install MCP memory server"
            echo "Please check your internet connection and try again"
            exit 1
        fi
    fi
    
    print_success "MCP memory server is available"
}

# Start the memory server
start_server() {
    print_info "Configuration:"
    echo "  Project Directory: $PROJECT_DIR"
    echo "  Memory Data Path:  $MEMORY_DATA_DIR"
    echo "  Log File:         $LOG_FILE"
    echo
    
    # Set environment variables
    export MEMORY_STORAGE_PATH="$MEMORY_DATA_DIR"
    
    print_info "Starting MCP Memory Server..."
    print_warning "Press Ctrl+C to stop the server"
    echo
    
    # Log startup information
    echo "[$(date)] Starting MCP Memory Server" >> "$LOG_FILE"
    echo "[$(date)] Project Directory: $PROJECT_DIR" >> "$LOG_FILE"
    echo "[$(date)] Memory Data Path: $MEMORY_DATA_DIR" >> "$LOG_FILE"
    
    # Start the server with error handling
    if npx -y @modelcontextprotocol/server-memory 2>&1 | tee -a "$LOG_FILE"; then
        print_success "MCP Memory Server stopped normally"
    else
        print_error "MCP Memory Server stopped with error"
        echo "Check the log file for details: $LOG_FILE"
    fi
    
    echo "[$(date)] MCP Memory Server stopped" >> "$LOG_FILE"
    
    echo
    print_info "Log file location: $LOG_FILE"
    print_info "Memory data location: $MEMORY_DATA_DIR"
}

# Cleanup function
cleanup() {
    echo
    print_info "Cleaning up..."
    echo "[$(date)] Server interrupted by user" >> "$LOG_FILE"
    exit 0
}

# Set up signal handlers
trap cleanup SIGINT SIGTERM

# Main execution
main() {
    print_header
    
    check_nodejs
    check_npx
    setup_directories
    check_mcp_server
    
    echo
    start_server
}

# Run main function
main "$@"
