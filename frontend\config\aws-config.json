{"aws": {"region": "us-east-1", "s3": {"bucket": "reality2-frontend", "region": "us-east-1", "acl": "public-read", "storageClass": "STANDARD", "serverSideEncryption": "AES256"}, "cloudfront": {"distributionId": "", "originDomainName": "reality2-frontend.s3.amazonaws.com", "aliases": ["cdn.reality2.app", "assets.reality2.app"], "priceClass": "PriceClass_All", "defaultCacheBehavior": {"targetOriginId": "S3-reality2-frontend", "viewerProtocolPolicy": "redirect-to-https", "allowedMethods": ["GET", "HEAD", "OPTIONS"], "cachedMethods": ["GET", "HEAD"], "compress": true, "cachePolicyId": "4135ea2d-6df8-44a3-9df3-4b5a84be39ad", "originRequestPolicyId": "88a5eaf4-2fd4-4709-b370-b4c650ea3fcf"}, "cacheBehaviors": [{"pathPattern": "*.html", "targetOriginId": "S3-reality2-frontend", "viewerProtocolPolicy": "redirect-to-https", "cachePolicyId": "4135ea2d-6df8-44a3-9df3-4b5a84be39ad", "ttl": {"defaultTTL": 3600, "maxTTL": 86400, "minTTL": 0}}, {"pathPattern": "/css/*", "targetOriginId": "S3-reality2-frontend", "viewerProtocolPolicy": "redirect-to-https", "cachePolicyId": "4135ea2d-6df8-44a3-9df3-4b5a84be39ad", "ttl": {"defaultTTL": 31536000, "maxTTL": 31536000, "minTTL": 31536000}}, {"pathPattern": "/js/*", "targetOriginId": "S3-reality2-frontend", "viewerProtocolPolicy": "redirect-to-https", "cachePolicyId": "4135ea2d-6df8-44a3-9df3-4b5a84be39ad", "ttl": {"defaultTTL": 31536000, "maxTTL": 31536000, "minTTL": 31536000}}, {"pathPattern": "/images/*", "targetOriginId": "S3-reality2-frontend", "viewerProtocolPolicy": "redirect-to-https", "cachePolicyId": "4135ea2d-6df8-44a3-9df3-4b5a84be39ad", "ttl": {"defaultTTL": 31536000, "maxTTL": 31536000, "minTTL": 31536000}}, {"pathPattern": "/fonts/*", "targetOriginId": "S3-reality2-frontend", "viewerProtocolPolicy": "redirect-to-https", "cachePolicyId": "4135ea2d-6df8-44a3-9df3-4b5a84be39ad", "ttl": {"defaultTTL": 31536000, "maxTTL": 31536000, "minTTL": 31536000}, "responseHeadersPolicy": {"corsConfig": {"accessControlAllowCredentials": false, "accessControlAllowHeaders": ["*"], "accessControlAllowMethods": ["GET", "HEAD"], "accessControlAllowOrigins": ["*"], "accessControlMaxAgeSec": 86400}}}], "customErrorResponses": [{"errorCode": 403, "responseCode": 200, "responsePage": "/index.html", "errorCachingMinTTL": 300}, {"errorCode": 404, "responseCode": 200, "responsePage": "/index.html", "errorCachingMinTTL": 300}]}}, "deployment": {"syncOptions": {"delete": true, "exclude": [".DS_Store", "*.map", "node_modules/*"], "include": ["*"]}, "cacheControl": {"*.html": "public, max-age=3600, s-maxage=3600", "*.css": "public, max-age=31536000, immutable", "*.js": "public, max-age=31536000, immutable", "*.png": "public, max-age=31536000, immutable", "*.jpg": "public, max-age=31536000, immutable", "*.jpeg": "public, max-age=31536000, immutable", "*.gif": "public, max-age=31536000, immutable", "*.svg": "public, max-age=31536000, immutable", "*.webp": "public, max-age=31536000, immutable", "*.avif": "public, max-age=31536000, immutable", "*.woff": "public, max-age=31536000, immutable", "*.woff2": "public, max-age=31536000, immutable", "*.ttf": "public, max-age=31536000, immutable", "*.eot": "public, max-age=31536000, immutable"}, "contentType": {"*.css": "text/css", "*.js": "application/javascript", "*.json": "application/json", "*.html": "text/html", "*.svg": "image/svg+xml", "*.woff": "font/woff", "*.woff2": "font/woff2", "*.ttf": "font/ttf", "*.eot": "application/vnd.ms-fontobject"}, "contentEncoding": {"*.gz": "gzip", "*.br": "br"}}, "monitoring": {"cloudWatch": {"enabled": true, "logGroup": "/aws/cloudfront/reality2-frontend", "retentionInDays": 30}, "alarms": [{"name": "HighErrorRate", "metric": "4xxErrorRate", "threshold": 5, "comparisonOperator": "GreaterThanThreshold", "evaluationPeriods": 2, "period": 300}, {"name": "HighLatency", "metric": "OriginLatency", "threshold": 1000, "comparisonOperator": "GreaterThanThreshold", "evaluationPeriods": 2, "period": 300}]}}