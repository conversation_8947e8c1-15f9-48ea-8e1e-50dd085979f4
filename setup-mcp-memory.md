# MCP Memory Server Setup Guide

## Overview

This guide will help you set up the MCP (Model Context Protocol) memory server locally on your Windows device for persistent memory management with Reality 2.0.

## Prerequisites

✅ **Node.js**: v22.16.0 (already installed)
✅ **npm/npx**: Available with Node.js

## Step 1: Fix PowerShell Execution Policy

The error you encountered is due to PowerShell execution policy restrictions. Here are the solutions:

### Option A: Temporary Fix (Recommended for testing)
Open PowerShell as Administrator and run:
```powershell
Set-ExecutionPolicy -ExecutionPolicy RemoteSigned -Scope CurrentUser
```

### Option B: Use Command Prompt instead
Open Command Prompt (cmd) and use npm/npx commands there instead of PowerShell.

### Option C: Bypass for single command
```powershell
powershell -ExecutionPolicy Bypass -Command "npx -y @modelcontextprotocol/server-memory --help"
```

## Step 2: Install and Test MCP Memory Server

### Using Command Prompt (Recommended)
1. Open Command Prompt (cmd)
2. Navigate to your project directory:
```cmd
cd C:\Users\<USER>\Documents\augment-projects\Reality-2.0
```

3. Test the MCP memory server installation:
```cmd
npx -y @modelcontextprotocol/server-memory --help
```

4. Start the memory server:
```cmd
npx -y @modelcontextprotocol/server-memory
```

## Step 3: Configure MCP Memory Server

### Default Configuration
The memory server will run on:
- **Protocol**: stdio (standard input/output)
- **Storage**: Local file-based storage
- **Location**: Current directory or specified path

### Custom Configuration
Create a configuration file `mcp-memory-config.json`:
```json
{
  "name": "memory",
  "command": "npx",
  "args": ["-y", "@modelcontextprotocol/server-memory"],
  "env": {
    "MEMORY_STORAGE_PATH": "./mcp-memory-data"
  }
}
```

## Step 4: Integration with Augment

### Add to Augment Settings
1. Open Augment settings
2. Navigate to Tools > MCP
3. Add a new MCP server with these settings:
   - **Name**: Memory Server
   - **Command**: `npx`
   - **Args**: `["-y", "@modelcontextprotocol/server-memory"]`
   - **Working Directory**: `C:\Users\<USER>\Documents\augment-projects\Reality-2.0`

### Alternative: Use batch script
Create `start-mcp-memory.bat` for easier management.

## Step 5: Verify Installation

### Test Commands
```cmd
# Check if server starts
npx -y @modelcontextprotocol/server-memory --version

# Test server functionality
npx -y @modelcontextprotocol/server-memory --test
```

### Expected Output
The server should start and show:
- Server name and version
- Available capabilities
- Storage location
- Ready status

## Troubleshooting

### Common Issues

1. **PowerShell Execution Policy**
   - Solution: Use Command Prompt or fix execution policy

2. **Network/Proxy Issues**
   - Solution: Configure npm proxy if behind corporate firewall
   ```cmd
   npm config set proxy http://proxy-server:port
   npm config set https-proxy http://proxy-server:port
   ```

3. **Permission Issues**
   - Solution: Run as Administrator or check folder permissions

4. **Node.js Version**
   - Requirement: Node.js 16+ (you have v22.16.0 ✅)

### Debug Commands
```cmd
# Check npm configuration
npm config list

# Check npx cache
npx --version

# Clear npx cache if needed
npx clear-npx-cache
```

## Usage Examples

### Basic Memory Operations
Once the server is running, you can:
- Store memories about project decisions
- Retrieve context from previous sessions
- Maintain conversation history
- Store development preferences

### Integration with Reality 2.0
The memory server will help maintain context about:
- Project structure and decisions
- API configurations (Mapy.cz keys, etc.)
- Development preferences
- Error patterns and solutions
- Deployment configurations

## Next Steps

1. **Test the setup** using the provided scripts
2. **Configure Augment** to use the memory server
3. **Verify functionality** with test memories
4. **Document any issues** for troubleshooting

## Files Created

- `setup-mcp-memory.md` - This setup guide
- `start-mcp-memory.bat` - Windows batch script to start server
- `start-mcp-memory.sh` - Linux/macOS script to start server
- `test-mcp-memory.js` - Test script to verify functionality

The memory server will enhance our development workflow by maintaining persistent context across sessions.
