# Reality 2.0 - MCP Memory Server PowerShell Script
# Starts the Model Context Protocol memory server for persistent memory management

param(
    [switch]$Test,
    [switch]$Help
)

# Configuration
$ProjectDir = $PSScriptRoot
$MemoryDataDir = Join-Path $ProjectDir "mcp-memory-data"
$LogFile = Join-Path $ProjectDir "mcp-memory.log"

# Colors for output
$Colors = @{
    Red = "Red"
    Green = "Green"
    Yellow = "Yellow"
    Blue = "Blue"
    Cyan = "Cyan"
    Magenta = "Magenta"
}

function Write-ColorOutput {
    param(
        [string]$Message,
        [string]$Color = "White"
    )
    Write-Host $Message -ForegroundColor $Color
}

function Write-Success {
    param([string]$Message)
    Write-ColorOutput "✅ $Message" $Colors.Green
}

function Write-Error {
    param([string]$Message)
    Write-ColorOutput "❌ $Message" $Colors.Red
}

function Write-Warning {
    param([string]$Message)
    Write-ColorOutput "⚠️  $Message" $Colors.Yellow
}

function Write-Info {
    param([string]$Message)
    Write-ColorOutput "ℹ️  $Message" $Colors.Blue
}

function Write-Header {
    Write-ColorOutput "🧠 Reality 2.0 - MCP Memory Server" $Colors.Cyan
    Write-ColorOutput "==================================" $Colors.Cyan
    Write-Host ""
}

function Show-Help {
    Write-Header
    Write-Host "Usage: .\start-mcp-memory.ps1 [OPTIONS]"
    Write-Host ""
    Write-Host "Options:"
    Write-Host "  -Test    Run tests to verify MCP memory server setup"
    Write-Host "  -Help    Show this help message"
    Write-Host ""
    Write-Host "Examples:"
    Write-Host "  .\start-mcp-memory.ps1           # Start the memory server"
    Write-Host "  .\start-mcp-memory.ps1 -Test     # Run tests"
    Write-Host "  .\start-mcp-memory.ps1 -Help     # Show help"
    Write-Host ""
    Write-Host "Note: If you get execution policy errors, run:"
    Write-Host "  Set-ExecutionPolicy -ExecutionPolicy RemoteSigned -Scope CurrentUser"
    Write-Host ""
}

function Test-Prerequisites {
    Write-Info "Checking prerequisites..."
    
    # Check Node.js
    try {
        $nodeVersion = node --version 2>$null
        if ($LASTEXITCODE -eq 0) {
            Write-Success "Node.js is available: $nodeVersion"
        } else {
            Write-Error "Node.js is not available"
            return $false
        }
    } catch {
        Write-Error "Node.js is not installed"
        return $false
    }
    
    # Check npx
    try {
        $npxVersion = npx --version 2>$null
        if ($LASTEXITCODE -eq 0) {
            Write-Success "npx is available"
        } else {
            Write-Error "npx is not available"
            return $false
        }
    } catch {
        Write-Error "npx is not installed"
        return $false
    }
    
    return $true
}

function Initialize-Directories {
    Write-Info "Setting up directories..."
    
    if (-not (Test-Path $MemoryDataDir)) {
        try {
            New-Item -ItemType Directory -Path $MemoryDataDir -Force | Out-Null
            Write-Success "Memory data directory created: $MemoryDataDir"
        } catch {
            Write-Error "Failed to create memory data directory: $($_.Exception.Message)"
            return $false
        }
    } else {
        Write-Success "Memory data directory exists: $MemoryDataDir"
    }
    
    return $true
}

function Test-McpServer {
    Write-Info "Testing MCP memory server availability..."
    
    try {
        $output = npx -y "@modelcontextprotocol/server-memory" --version 2>&1
        if ($LASTEXITCODE -eq 0) {
            Write-Success "MCP memory server is available"
            return $true
        } else {
            Write-Warning "MCP memory server may need installation"
            Write-Info "Output: $output"
            return $false
        }
    } catch {
        Write-Error "Error checking MCP server: $($_.Exception.Message)"
        return $false
    }
}

function Start-McpServer {
    Write-Info "Configuration:"
    Write-Host "  Project Directory: $ProjectDir"
    Write-Host "  Memory Data Path:  $MemoryDataDir"
    Write-Host "  Log File:         $LogFile"
    Write-Host ""
    
    # Set environment variable
    $env:MEMORY_STORAGE_PATH = $MemoryDataDir
    
    Write-Info "Starting MCP Memory Server..."
    Write-Warning "Press Ctrl+C to stop the server"
    Write-Host ""
    
    # Log startup
    $timestamp = Get-Date -Format "yyyy-MM-dd HH:mm:ss"
    Add-Content -Path $LogFile -Value "[$timestamp] Starting MCP Memory Server"
    Add-Content -Path $LogFile -Value "[$timestamp] Project Directory: $ProjectDir"
    Add-Content -Path $LogFile -Value "[$timestamp] Memory Data Path: $MemoryDataDir"
    
    try {
        # Start the server
        npx -y "@modelcontextprotocol/server-memory" 2>&1 | Tee-Object -FilePath $LogFile -Append
        
        if ($LASTEXITCODE -eq 0) {
            Write-Success "MCP Memory Server stopped normally"
        } else {
            Write-Error "MCP Memory Server stopped with error"
            Write-Info "Check the log file for details: $LogFile"
        }
    } catch {
        Write-Error "Error starting MCP server: $($_.Exception.Message)"
    } finally {
        $timestamp = Get-Date -Format "yyyy-MM-dd HH:mm:ss"
        Add-Content -Path $LogFile -Value "[$timestamp] MCP Memory Server stopped"
        
        Write-Host ""
        Write-Info "Log file location: $LogFile"
        Write-Info "Memory data location: $MemoryDataDir"
    }
}

function Run-Tests {
    Write-Header
    Write-Info "Running MCP Memory Server tests..."
    Write-Host ""
    
    $testResults = @{}
    
    # Test prerequisites
    $testResults["Prerequisites"] = Test-Prerequisites
    
    # Test directory setup
    $testResults["Directory Setup"] = Initialize-Directories
    
    # Test MCP server
    $testResults["MCP Server"] = Test-McpServer
    
    # Summary
    Write-Host ""
    Write-ColorOutput "Test Summary:" $Colors.Cyan
    Write-Host "=" * 30
    
    $passed = 0
    $total = $testResults.Count
    
    foreach ($test in $testResults.GetEnumerator()) {
        if ($test.Value) {
            Write-Success "$($test.Key): PASS"
            $passed++
        } else {
            Write-Error "$($test.Key): FAIL"
        }
    }
    
    Write-Host "=" * 30
    Write-ColorOutput "Total: $total, Passed: $passed, Failed: $($total - $passed)" $Colors.Cyan
    
    if ($passed -eq $total) {
        Write-Success "All tests passed! MCP memory server is ready to use."
        return $true
    } else {
        Write-Warning "Some tests failed. Check the setup guide for troubleshooting."
        return $false
    }
}

# Main execution
if ($Help) {
    Show-Help
    exit 0
}

if ($Test) {
    $success = Run-Tests
    exit $(if ($success) { 0 } else { 1 })
}

# Default: Start the server
Write-Header

if (-not (Test-Prerequisites)) {
    Write-Error "Prerequisites check failed. Please install Node.js and npm."
    exit 1
}

if (-not (Initialize-Directories)) {
    Write-Error "Directory setup failed."
    exit 1
}

Start-McpServer
