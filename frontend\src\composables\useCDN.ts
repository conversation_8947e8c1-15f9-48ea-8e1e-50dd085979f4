/**
 * Vue Composable for CDN Integration
 * Provides reactive CDN functionality for Vue components
 */

import { ref, computed, onMounted, watch } from 'vue'
import { cdnService } from '@/services/cdnService'
import type { ImageOptimizationOptions, AssetLoadOptions } from '@/services/cdnService'

export interface UseCDNOptions {
  autoLoad?: boolean
  fallback?: string
  priority?: 'high' | 'medium' | 'low'
}

export function useCDN() {
  const status = ref(cdnService.getStatus())
  
  // Update status periodically
  const updateStatus = () => {
    status.value = cdnService.getStatus()
  }
  
  onMounted(() => {
    updateStatus()
    // Update status every 30 seconds
    const interval = setInterval(updateStatus, 30000)
    
    // Cleanup on unmount
    return () => clearInterval(interval)
  })
  
  return {
    status: computed(() => status.value),
    isEnabled: computed(() => status.value.enabled),
    isHealthy: computed(() => status.value.healthy),
    
    // Asset loading methods
    loadAsset: cdnService.loadAsset.bind(cdnService),
    loadImage: cdnService.loadImage.bind(cdnService),
    loadFont: cdnService.loadFont.bind(cdnService),
    loadCSS: cdnService.loadCSS.bind(cdnService),
    loadScript: cdnService.loadScript.bind(cdnService),
    
    // Utility methods
    getResponsiveImageSrcSet: cdnService.getResponsiveImageSrcSet.bind(cdnService),
    preloadPageAssets: cdnService.preloadPageAssets.bind(cdnService),
    clearFailedAssets: cdnService.clearFailedAssets.bind(cdnService),
  }
}

export function useImage(path: string, options: ImageOptimizationOptions & UseCDNOptions = {}) {
  const { autoLoad = true, fallback, priority, ...imageOptions } = options
  
  const loading = ref(false)
  const error = ref<Error | null>(null)
  const src = ref<string>('')
  
  const load = async () => {
    if (!path) return
    
    loading.value = true
    error.value = null
    
    try {
      const url = await cdnService.loadImage(path, imageOptions)
      src.value = url
    } catch (err) {
      error.value = err as Error
      if (fallback) {
        src.value = fallback
      }
    } finally {
      loading.value = false
    }
  }
  
  const reload = () => {
    cdnService.clearFailedAssets()
    load()
  }
  
  // Auto-load on mount if enabled
  if (autoLoad) {
    onMounted(load)
  }
  
  // Watch for path changes
  watch(() => path, load, { immediate: autoLoad })
  
  return {
    src: computed(() => src.value),
    loading: computed(() => loading.value),
    error: computed(() => error.value),
    load,
    reload
  }
}

export function useResponsiveImage(
  path: string, 
  widths: number[] = [320, 640, 768, 1024, 1280, 1920],
  options: ImageOptimizationOptions & UseCDNOptions = {}
) {
  const { autoLoad = true, ...imageOptions } = options
  
  const loading = ref(false)
  const error = ref<Error | null>(null)
  const srcSet = ref<string>('')
  const src = ref<string>('')
  
  const load = async () => {
    if (!path) return
    
    loading.value = true
    error.value = null
    
    try {
      // Generate responsive srcset
      srcSet.value = cdnService.getResponsiveImageSrcSet(path, widths)
      
      // Load default size image
      const defaultWidth = widths[Math.floor(widths.length / 2)]
      src.value = await cdnService.loadImage(path, {
        ...imageOptions,
        width: defaultWidth
      })
    } catch (err) {
      error.value = err as Error
      src.value = path
    } finally {
      loading.value = false
    }
  }
  
  if (autoLoad) {
    onMounted(load)
  }
  
  watch(() => path, load, { immediate: autoLoad })
  
  return {
    src: computed(() => src.value),
    srcSet: computed(() => srcSet.value),
    loading: computed(() => loading.value),
    error: computed(() => error.value),
    load
  }
}

export function useFont(path: string, options: AssetLoadOptions & UseCDNOptions = {}) {
  const { autoLoad = true, ...loadOptions } = options
  
  const loading = ref(false)
  const error = ref<Error | null>(null)
  const loaded = ref(false)
  const url = ref<string>('')
  
  const load = async () => {
    if (!path) return
    
    loading.value = true
    error.value = null
    
    try {
      url.value = await cdnService.loadFont(path, loadOptions)
      loaded.value = true
    } catch (err) {
      error.value = err as Error
    } finally {
      loading.value = false
    }
  }
  
  if (autoLoad) {
    onMounted(load)
  }
  
  return {
    url: computed(() => url.value),
    loading: computed(() => loading.value),
    loaded: computed(() => loaded.value),
    error: computed(() => error.value),
    load
  }
}

export function useAssetPreloader() {
  const preloadedAssets = ref<Set<string>>(new Set())
  const preloadingAssets = ref<Set<string>>(new Set())
  
  const preload = async (assets: string[]) => {
    const newAssets = assets.filter(asset => !preloadedAssets.value.has(asset))
    
    newAssets.forEach(asset => preloadingAssets.value.add(asset))
    
    try {
      await Promise.all(
        newAssets.map(async asset => {
          try {
            await cdnService.loadAsset(asset, { priority: 'low' })
            preloadedAssets.value.add(asset)
          } catch (error) {
            console.warn(`Failed to preload asset: ${asset}`, error)
          } finally {
            preloadingAssets.value.delete(asset)
          }
        })
      )
    } catch (error) {
      console.warn('Asset preloading failed:', error)
    }
  }
  
  const preloadImages = (imagePaths: string[], options: ImageOptimizationOptions = {}) => {
    const imagePromises = imagePaths.map(path => 
      cdnService.loadImage(path, { ...options, lazy: false })
    )
    
    return Promise.allSettled(imagePromises)
  }
  
  const preloadFonts = (fontPaths: string[]) => {
    const fontPromises = fontPaths.map(path => 
      cdnService.loadFont(path, { priority: 'high' })
    )
    
    return Promise.allSettled(fontPromises)
  }
  
  return {
    preloadedAssets: computed(() => Array.from(preloadedAssets.value)),
    preloadingAssets: computed(() => Array.from(preloadingAssets.value)),
    preload,
    preloadImages,
    preloadFonts
  }
}

export function useCDNStatus() {
  const { status, isEnabled, isHealthy } = useCDN()
  
  const statusText = computed(() => {
    if (!isEnabled.value) return 'Disabled'
    if (!isHealthy.value) return 'Unhealthy'
    return 'Healthy'
  })
  
  const statusColor = computed(() => {
    if (!isEnabled.value) return 'gray'
    if (!isHealthy.value) return 'red'
    return 'green'
  })
  
  return {
    status,
    isEnabled,
    isHealthy,
    statusText,
    statusColor,
    provider: computed(() => status.value.provider),
    loadedAssets: computed(() => status.value.loadedAssets),
    failedAssets: computed(() => status.value.failedAssets)
  }
}
