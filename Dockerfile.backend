# Multi-stage Dockerfile for Reality 2.0 FastAPI backend
# Optimized for production with development support

# Base Python image
FROM python:3.11-slim as base

# Set environment variables
ENV PYTHONUNBUFFERED=1 \
    PYTHONDONTWRITEBYTECODE=1 \
    PIP_NO_CACHE_DIR=1 \
    PIP_DISABLE_PIP_VERSION_CHECK=1 \
    PYTHONPATH=/app

# Install system dependencies
RUN apt-get update && apt-get install -y \
    build-essential \
    curl \
    && rm -rf /var/lib/apt/lists/*

# Create app user
RUN groupadd -r appuser && useradd -r -g appuser appuser

# Set work directory
WORKDIR /app

# Install Python dependencies
COPY backend/requirements.txt .
RUN pip install --no-cache-dir -r requirements.txt

# Development stage
FROM base as development

# Install additional development dependencies
RUN pip install --no-cache-dir \
    black==23.12.0 \
    flake8==6.1.0 \
    mypy==1.8.0 \
    pre-commit==3.6.0

# Copy application code
COPY backend/ .

# Create necessary directories
RUN mkdir -p logs uploads

# Change ownership to app user
RUN chown -R appuser:appuser /app

# Switch to app user
USER appuser

# Expose port
EXPOSE 8000

# Health check
HEALTHCHECK --interval=30s --timeout=10s --start-period=5s --retries=3 \
    CMD curl -f http://localhost:8000/health || exit 1

# Development command - using reality_app.py directly
CMD ["uvicorn", "reality_app:app", "--host", "0.0.0.0", "--port", "8000", "--reload"]

# Production stage
FROM base as production

# Copy application code
COPY backend/ .

# Create necessary directories
RUN mkdir -p logs uploads

# Change ownership to app user
RUN chown -R appuser:appuser /app

# Switch to app user
USER appuser

# Expose port
EXPOSE 8000

# Health check
HEALTHCHECK --interval=30s --timeout=10s --start-period=5s --retries=3 \
    CMD curl -f http://localhost:8000/health || exit 1

# Production command - using reality_app.py with multiple workers
CMD ["uvicorn", "reality_app:app", "--host", "0.0.0.0", "--port", "8000", "--workers", "4"]
