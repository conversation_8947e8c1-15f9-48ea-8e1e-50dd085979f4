# Multi-stage Dockerfile for Reality 2.0 Vue.js frontend
# Optimized for production with development support

# Base Node.js image
FROM node:18-alpine as base

# Set working directory
WORKDIR /app

# Development stage
FROM base as development

# Copy package files
COPY frontend/package*.json ./

# Install dependencies using npm (since we have package-lock.json)
RUN npm ci

# Copy source code
COPY frontend/ .

# Create app user
RUN addgroup -g 1001 -S nodejs && \
    adduser -S vueuser -u 1001 -G nodejs

# Change ownership
RUN chown -R vueuser:nodejs /app
USER vueuser

# Expose port
EXPOSE 3000

# Health check
HEALTHCHECK --interval=30s --timeout=10s --start-period=5s --retries=3 \
    CMD curl -f http://localhost:3000 || exit 1

# Development command
CMD ["npm", "run", "dev"]

# Build stage
FROM base as builder

# Copy package files
COPY frontend/package*.json ./

# Install dependencies
RUN npm ci

# Copy source code
COPY frontend/ .

# Build application with CDN optimization
ARG VITE_CDN_ENABLED=false
ARG VITE_CDN_URL=""
ENV VITE_CDN_ENABLED=$VITE_CDN_ENABLED
ENV VITE_CDN_URL=$VITE_CDN_URL

RUN if [ "$VITE_CDN_ENABLED" = "true" ]; then \
      npm run build:cdn; \
    else \
      npm run build; \
    fi

# Production stage
FROM nginx:alpine as production

# Install curl for health checks
RUN apk add --no-cache curl

# Copy built application
COPY --from=builder /app/dist /usr/share/nginx/html

# Create custom nginx configuration for Vue.js SPA
RUN echo 'server {' > /etc/nginx/conf.d/default.conf && \
    echo '    listen 80;' >> /etc/nginx/conf.d/default.conf && \
    echo '    server_name localhost;' >> /etc/nginx/conf.d/default.conf && \
    echo '    root /usr/share/nginx/html;' >> /etc/nginx/conf.d/default.conf && \
    echo '    index index.html;' >> /etc/nginx/conf.d/default.conf && \
    echo '    location / {' >> /etc/nginx/conf.d/default.conf && \
    echo '        try_files $uri $uri/ /index.html;' >> /etc/nginx/conf.d/default.conf && \
    echo '    }' >> /etc/nginx/conf.d/default.conf && \
    echo '    location /api/ {' >> /etc/nginx/conf.d/default.conf && \
    echo '        proxy_pass http://backend:8000;' >> /etc/nginx/conf.d/default.conf && \
    echo '        proxy_set_header Host $host;' >> /etc/nginx/conf.d/default.conf && \
    echo '        proxy_set_header X-Real-IP $remote_addr;' >> /etc/nginx/conf.d/default.conf && \
    echo '        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;' >> /etc/nginx/conf.d/default.conf && \
    echo '        proxy_set_header X-Forwarded-Proto $scheme;' >> /etc/nginx/conf.d/default.conf && \
    echo '    }' >> /etc/nginx/conf.d/default.conf && \
    echo '}' >> /etc/nginx/conf.d/default.conf

# Expose port
EXPOSE 80

# Health check
HEALTHCHECK --interval=30s --timeout=10s --start-period=5s --retries=3 \
    CMD curl -f http://localhost:80 || exit 1

# Start nginx
CMD ["nginx", "-g", "daemon off;"]
