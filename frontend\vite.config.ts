import { defineConfig, loadEnv } from 'vite'
import vue from '@vitejs/plugin-vue'
import { resolve } from 'path'

// https://vitejs.dev/config/
export default defineConfig(({ command, mode }) => {
  // Load env file based on `mode` in the current working directory.
  const env = loadEnv(mode, process.cwd(), '')

  // CDN configuration
  const cdnUrl = env.VITE_CDN_URL || ''
  const isProduction = mode === 'production'
  const useCdn = isProduction && cdnUrl

  return {
    plugins: [vue()],
    
    // Path resolution
    resolve: {
      alias: {
        '@': resolve(__dirname, 'src'),
        '@components': resolve(__dirname, 'src/components'),
        '@views': resolve(__dirname, 'src/views'),
        '@stores': resolve(__dirname, 'src/stores'),
        '@services': resolve(__dirname, 'src/services'),
        '@composables': resolve(__dirname, 'src/composables'),
        '@types': resolve(__dirname, 'src/types'),
        '@assets': resolve(__dirname, 'src/assets'),
      },
    },
    
    // Development server configuration
    server: {
      host: '0.0.0.0',
      port: 3000,
      strictPort: true,
      hmr: {
        port: 3001,
      },
      proxy: {
        '/api': {
          target: env.VITE_API_BASE_URL || 'http://localhost:8000',
          changeOrigin: true,
          secure: false,
        },
      },
    },
    
    // Build configuration
    build: {
      outDir: 'dist',
      assetsDir: 'assets',
      sourcemap: mode === 'development',
      minify: mode === 'production' ? 'esbuild' : false,
      target: 'esnext',

      // CDN base URL for production builds
      ...(useCdn && {
        base: cdnUrl.endsWith('/') ? cdnUrl : `${cdnUrl}/`
      }),
      rollupOptions: {
        output: {
          // Optimized chunk splitting for CDN caching
          manualChunks: {
            // Core framework chunks
            'vendor-vue': ['vue'],
            'vendor-router': ['vue-router'],
            'vendor-store': ['pinia'],

            // UI library chunks
            'ui-headless': ['@headlessui/vue'],
            'ui-icons': ['@heroicons/vue'],

            // Utility chunks
            'utils-http': ['axios'],
            'utils-composables': ['@vueuse/core'],
            'utils-lodash': ['lodash-es'],
          },

          // Optimized file naming for CDN caching
          chunkFileNames: (chunkInfo) => {
            const facadeModuleId = chunkInfo.facadeModuleId
              ? chunkInfo.facadeModuleId.split('/').pop()
              : 'chunk'
            return `js/[name]-[hash].js`
          },
          entryFileNames: 'js/[name]-[hash].js',
          assetFileNames: (assetInfo) => {
            const info = assetInfo.name.split('.')
            const ext = info[info.length - 1]
            if (/\.(png|jpe?g|gif|svg|webp|avif)$/i.test(assetInfo.name)) {
              return `images/[name]-[hash][extname]`
            }
            if (/\.(woff2?|eot|ttf|otf)$/i.test(assetInfo.name)) {
              return `fonts/[name]-[hash][extname]`
            }
            if (/\.css$/i.test(assetInfo.name)) {
              return `css/[name]-[hash][extname]`
            }
            return `assets/[name]-[hash][extname]`
          },
        },
      },
      chunkSizeWarningLimit: 1000,

      // CDN optimization settings
      assetsInlineLimit: 4096, // Inline assets smaller than 4KB
      cssCodeSplit: true, // Split CSS for better caching
      reportCompressedSize: false, // Faster builds
    },
    
    // CSS configuration
    css: {
      devSourcemap: true,
      preprocessorOptions: {
        scss: {
          additionalData: `@import "@/assets/styles/variables.scss";`,
        },
      },
    },
    
    // Environment variables
    define: {
      __APP_VERSION__: JSON.stringify(process.env.npm_package_version),
      __BUILD_TIME__: JSON.stringify(new Date().toISOString()),
    },
    
    // Optimization
    optimizeDeps: {
      include: [
        'vue',
        'vue-router',
        'pinia',
        'axios',
        '@vueuse/core',
        '@headlessui/vue',
        '@heroicons/vue/24/outline',
        '@heroicons/vue/24/solid',
      ],
    },
    
    // Preview server (for production build testing)
    preview: {
      host: '0.0.0.0',
      port: 3000,
      strictPort: true,
    },
  }
})
