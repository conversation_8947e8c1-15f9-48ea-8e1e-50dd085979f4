@echo off
REM Reality 2.0 - MCP Memory Server Startup Script
REM Starts the Model Context Protocol memory server for persistent memory management

setlocal enabledelayedexpansion

REM Configuration
set "PROJECT_DIR=%~dp0"
set "MEMORY_DATA_DIR=%PROJECT_DIR%mcp-memory-data"
set "LOG_FILE=%PROJECT_DIR%mcp-memory.log"

REM Colors (limited support in Windows)
set "GREEN=[92m"
set "YELLOW=[93m"
set "RED=[91m"
set "BLUE=[94m"
set "NC=[0m"

echo %BLUE%🧠 Reality 2.0 - MCP Memory Server%NC%
echo %BLUE%==================================%NC%
echo.

REM Check if Node.js is available
node --version >nul 2>&1
if errorlevel 1 (
    echo %RED%❌ Node.js is not installed or not in PATH%NC%
    echo Please install Node.js from https://nodejs.org/
    pause
    exit /b 1
)

echo %GREEN%✅ Node.js is available: %NC%
node --version

REM Check if npx is available
npx --version >nul 2>&1
if errorlevel 1 (
    echo %RED%❌ npx is not available%NC%
    echo Please ensure npm is properly installed
    pause
    exit /b 1
)

echo %GREEN%✅ npx is available%NC%

REM Create memory data directory if it doesn't exist
if not exist "%MEMORY_DATA_DIR%" (
    echo %BLUE%📁 Creating memory data directory...%NC%
    mkdir "%MEMORY_DATA_DIR%"
    echo %GREEN%✅ Memory data directory created: %MEMORY_DATA_DIR%%NC%
)

REM Set environment variables
set "MEMORY_STORAGE_PATH=%MEMORY_DATA_DIR%"

echo.
echo %BLUE%📋 Configuration:%NC%
echo   Project Directory: %PROJECT_DIR%
echo   Memory Data Path:  %MEMORY_DATA_DIR%
echo   Log File:         %LOG_FILE%
echo.

REM Check if server package is available
echo %BLUE%🔍 Checking MCP memory server availability...%NC%
npx -y @modelcontextprotocol/server-memory --version >nul 2>&1
if errorlevel 1 (
    echo %YELLOW%⚠️  MCP memory server not found, installing...%NC%
    echo %BLUE%📦 Installing @modelcontextprotocol/server-memory...%NC%
    npx -y @modelcontextprotocol/server-memory --help >nul 2>&1
    if errorlevel 1 (
        echo %RED%❌ Failed to install MCP memory server%NC%
        echo Please check your internet connection and try again
        pause
        exit /b 1
    )
)

echo %GREEN%✅ MCP memory server is available%NC%

REM Start the memory server
echo.
echo %BLUE%🚀 Starting MCP Memory Server...%NC%
echo %YELLOW%Press Ctrl+C to stop the server%NC%
echo.

REM Log startup information
echo [%date% %time%] Starting MCP Memory Server >> "%LOG_FILE%"
echo [%date% %time%] Project Directory: %PROJECT_DIR% >> "%LOG_FILE%"
echo [%date% %time%] Memory Data Path: %MEMORY_DATA_DIR% >> "%LOG_FILE%"

REM Start the server with error handling
npx -y @modelcontextprotocol/server-memory 2>&1 | tee -a "%LOG_FILE%"

REM Check exit code
if errorlevel 1 (
    echo.
    echo %RED%❌ MCP Memory Server stopped with error%NC%
    echo Check the log file for details: %LOG_FILE%
) else (
    echo.
    echo %GREEN%✅ MCP Memory Server stopped normally%NC%
)

echo [%date% %time%] MCP Memory Server stopped >> "%LOG_FILE%"

echo.
echo %BLUE%📝 Log file location: %LOG_FILE%%NC%
echo %BLUE%💾 Memory data location: %MEMORY_DATA_DIR%%NC%
echo.
pause

endlocal
