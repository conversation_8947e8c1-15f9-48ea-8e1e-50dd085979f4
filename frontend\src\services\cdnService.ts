/**
 * CDN Service for Reality 2.0 Frontend
 * Handles asset loading, optimization, and fallback strategies
 */

import { 
  CDN_CONFIG, 
  buildCdnUrl, 
  buildImageUrl, 
  buildFontUrl, 
  buildStaticUrl,
  checkCdnHealth,
  preloadCriticalAssets
} from '@/config/cdn'

export interface AssetLoadOptions {
  fallback?: string
  timeout?: number
  retries?: number
  priority?: 'high' | 'medium' | 'low'
  crossOrigin?: 'anonymous' | 'use-credentials'
}

export interface ImageOptimizationOptions {
  width?: number
  height?: number
  format?: 'webp' | 'avif' | 'jpg' | 'png'
  quality?: number
  lazy?: boolean
  placeholder?: string
}

class CDNService {
  private healthStatus: boolean = true
  private loadedAssets: Set<string> = new Set()
  private failedAssets: Set<string> = new Set()
  
  constructor() {
    this.initialize()
  }
  
  private async initialize(): Promise<void> {
    if (CDN_CONFIG.enabled) {
      // Check CDN health on initialization
      this.healthStatus = await checkCdnHealth()
      
      // Preload critical assets
      preloadCriticalAssets()
      
      // Set up periodic health checks
      setInterval(() => this.performHealthCheck(), 5 * 60 * 1000) // Every 5 minutes
    }
  }
  
  private async performHealthCheck(): Promise<void> {
    this.healthStatus = await checkCdnHealth()
    if (!this.healthStatus) {
      console.warn('CDN health check failed, using fallback strategy')
    }
  }
  
  /**
   * Load an asset with fallback strategy
   */
  async loadAsset(path: string, options: AssetLoadOptions = {}): Promise<string> {
    const {
      fallback = path,
      timeout = 10000,
      retries = 2,
      priority = 'medium'
    } = options
    
    // If CDN is disabled or unhealthy, use fallback immediately
    if (!CDN_CONFIG.enabled || !this.healthStatus) {
      return this.loadFallbackAsset(fallback)
    }
    
    const cdnUrl = buildCdnUrl(path)
    
    // Check if asset was previously failed
    if (this.failedAssets.has(cdnUrl)) {
      return this.loadFallbackAsset(fallback)
    }
    
    // Check if asset is already loaded
    if (this.loadedAssets.has(cdnUrl)) {
      return cdnUrl
    }
    
    try {
      const loadedUrl = await this.loadWithTimeout(cdnUrl, timeout, retries)
      this.loadedAssets.add(cdnUrl)
      return loadedUrl
    } catch (error) {
      console.warn(`Failed to load asset from CDN: ${cdnUrl}`, error)
      this.failedAssets.add(cdnUrl)
      return this.loadFallbackAsset(fallback)
    }
  }
  
  /**
   * Load an optimized image with responsive options
   */
  async loadImage(path: string, options: ImageOptimizationOptions = {}): Promise<string> {
    const {
      width,
      height,
      format,
      quality = 85,
      lazy = true
    } = options
    
    // Build optimized image URL
    const imageUrl = buildImageUrl(path, {
      width,
      height,
      format: format || this.getBestImageFormat(),
      quality
    })
    
    return this.loadAsset(imageUrl, {
      fallback: path,
      priority: lazy ? 'low' : 'high'
    })
  }
  
  /**
   * Load a font with proper preloading
   */
  async loadFont(path: string, options: AssetLoadOptions = {}): Promise<string> {
    const fontUrl = buildFontUrl(path)
    
    // Preload font for better performance
    if (CDN_CONFIG.enabled && this.healthStatus) {
      this.preloadFont(fontUrl)
    }
    
    return this.loadAsset(fontUrl, {
      ...options,
      crossOrigin: 'anonymous',
      priority: 'high'
    })
  }
  
  /**
   * Load CSS with critical path optimization
   */
  async loadCSS(path: string, critical: boolean = false): Promise<string> {
    const cssUrl = buildStaticUrl(path)
    
    if (critical) {
      // Inline critical CSS
      return this.inlineCSS(cssUrl)
    }
    
    return this.loadAsset(cssUrl, {
      priority: 'high'
    })
  }
  
  /**
   * Load JavaScript with module support
   */
  async loadScript(path: string, options: AssetLoadOptions & { module?: boolean } = {}): Promise<string> {
    const { module = false, ...loadOptions } = options
    const scriptUrl = buildStaticUrl(path)
    
    return this.loadAsset(scriptUrl, {
      ...loadOptions,
      priority: 'medium'
    })
  }
  
  /**
   * Get responsive image srcset for different screen densities
   */
  getResponsiveImageSrcSet(path: string, widths: number[]): string {
    return widths
      .map(width => {
        const url = buildImageUrl(path, { 
          width, 
          format: this.getBestImageFormat(),
          quality: 85 
        })
        return `${url} ${width}w`
      })
      .join(', ')
  }
  
  /**
   * Preload critical resources for the current page
   */
  preloadPageAssets(assets: string[]): void {
    assets.forEach(asset => {
      this.preloadAsset(asset)
    })
  }
  
  /**
   * Clear failed assets cache (for retry scenarios)
   */
  clearFailedAssets(): void {
    this.failedAssets.clear()
  }
  
  /**
   * Get CDN status information
   */
  getStatus(): {
    enabled: boolean
    healthy: boolean
    loadedAssets: number
    failedAssets: number
    provider: string
  } {
    return {
      enabled: CDN_CONFIG.enabled,
      healthy: this.healthStatus,
      loadedAssets: this.loadedAssets.size,
      failedAssets: this.failedAssets.size,
      provider: CDN_CONFIG.providers.primary.name
    }
  }
  
  // Private helper methods
  
  private async loadWithTimeout(url: string, timeout: number, retries: number): Promise<string> {
    for (let attempt = 0; attempt <= retries; attempt++) {
      try {
        const response = await Promise.race([
          fetch(url, { method: 'HEAD' }),
          new Promise<never>((_, reject) => 
            setTimeout(() => reject(new Error('Timeout')), timeout)
          )
        ])
        
        if (response.ok) {
          return url
        }
        
        throw new Error(`HTTP ${response.status}`)
      } catch (error) {
        if (attempt === retries) {
          throw error
        }
        
        // Exponential backoff
        await new Promise(resolve => setTimeout(resolve, Math.pow(2, attempt) * 1000))
      }
    }
    
    throw new Error('Max retries exceeded')
  }
  
  private loadFallbackAsset(fallback: string): string {
    // Ensure fallback URL is properly formatted
    return fallback.startsWith('/') ? fallback : `/${fallback}`
  }
  
  private getBestImageFormat(): string {
    // Feature detection for modern image formats
    if (this.supportsImageFormat('avif')) return 'avif'
    if (this.supportsImageFormat('webp')) return 'webp'
    return 'jpg'
  }
  
  private supportsImageFormat(format: string): boolean {
    const canvas = document.createElement('canvas')
    canvas.width = 1
    canvas.height = 1
    
    try {
      return canvas.toDataURL(`image/${format}`).indexOf(`data:image/${format}`) === 0
    } catch {
      return false
    }
  }
  
  private preloadAsset(url: string): void {
    const link = document.createElement('link')
    link.rel = 'preload'
    link.href = url
    
    // Determine asset type
    if (url.includes('.css')) {
      link.as = 'style'
    } else if (url.includes('.js')) {
      link.as = 'script'
    } else if (url.match(/\.(woff2?|eot|ttf|otf)$/)) {
      link.as = 'font'
      link.crossOrigin = 'anonymous'
    } else if (url.match(/\.(jpg|jpeg|png|webp|avif|svg)$/)) {
      link.as = 'image'
    }
    
    document.head.appendChild(link)
  }
  
  private preloadFont(url: string): void {
    const link = document.createElement('link')
    link.rel = 'preload'
    link.href = url
    link.as = 'font'
    link.crossOrigin = 'anonymous'
    document.head.appendChild(link)
  }
  
  private async inlineCSS(url: string): Promise<string> {
    try {
      const response = await fetch(url)
      const css = await response.text()
      
      const style = document.createElement('style')
      style.textContent = css
      document.head.appendChild(style)
      
      return url
    } catch (error) {
      console.warn('Failed to inline CSS:', error)
      return this.loadFallbackAsset(url)
    }
  }
}

// Export singleton instance
export const cdnService = new CDNService()
export default cdnService
