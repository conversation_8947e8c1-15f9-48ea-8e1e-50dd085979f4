#!/usr/bin/env node

/**
 * CDN Deployment Script for Reality 2.0 Frontend
 * Deploys built assets to CDN providers with optimization
 */

import fs from 'fs'
import path from 'path'
import { fileURLToPath } from 'url'
import { execSync } from 'child_process'

const __filename = fileURLToPath(import.meta.url)
const __dirname = path.dirname(__filename)

// Configuration
const CONFIG = {
  distDir: path.resolve(__dirname, '../dist'),
  assetsDir: path.resolve(__dirname, '../dist/assets'),
  
  // CDN Providers
  providers: {
    cloudflare: {
      name: '<PERSON>flare',
      command: 'wrangler',
      uploadCommand: 'wrangler pages publish',
      configFile: 'wrangler.toml'
    },
    aws: {
      name: 'AWS S3 + CloudFront',
      command: 'aws',
      uploadCommand: 'aws s3 sync',
      configFile: 'aws-config.json'
    },
    azure: {
      name: 'Azure CDN',
      command: 'az',
      uploadCommand: 'az storage blob upload-batch',
      configFile: 'azure-config.json'
    },
    gcp: {
      name: 'Google Cloud CDN',
      command: 'gsutil',
      uploadCommand: 'gsutil -m rsync -r -d',
      configFile: 'gcp-config.json'
    }
  },
  
  // Optimization settings
  optimization: {
    gzip: true,
    brotli: true,
    imageOptimization: true,
    cssMinification: true,
    jsMinification: true
  },
  
  // Cache headers
  cacheHeaders: {
    'text/html': 'public, max-age=3600, s-maxage=3600',
    'text/css': 'public, max-age=31536000, immutable',
    'application/javascript': 'public, max-age=31536000, immutable',
    'image/*': 'public, max-age=31536000, immutable',
    'font/*': 'public, max-age=31536000, immutable'
  }
}

class CDNDeployer {
  constructor(provider = 'cloudflare') {
    this.provider = provider
    this.config = CONFIG.providers[provider]
    this.startTime = Date.now()
    
    if (!this.config) {
      throw new Error(`Unknown CDN provider: ${provider}`)
    }
  }
  
  async deploy() {
    console.log(`🚀 Starting CDN deployment to ${this.config.name}...`)
    
    try {
      // Pre-deployment checks
      await this.preDeploymentChecks()
      
      // Optimize assets
      await this.optimizeAssets()
      
      // Deploy to CDN
      await this.deployToProvider()
      
      // Post-deployment verification
      await this.postDeploymentVerification()
      
      const duration = ((Date.now() - this.startTime) / 1000).toFixed(2)
      console.log(`✅ CDN deployment completed successfully in ${duration}s`)
      
    } catch (error) {
      console.error('❌ CDN deployment failed:', error.message)
      process.exit(1)
    }
  }
  
  async preDeploymentChecks() {
    console.log('🔍 Running pre-deployment checks...')
    
    // Check if dist directory exists
    if (!fs.existsSync(CONFIG.distDir)) {
      throw new Error('Dist directory not found. Run "npm run build" first.')
    }
    
    // Check if provider CLI is available
    try {
      execSync(`${this.config.command} --version`, { stdio: 'ignore' })
    } catch (error) {
      throw new Error(`${this.config.name} CLI not found. Please install ${this.config.command}.`)
    }
    
    // Check configuration file
    const configPath = path.resolve(__dirname, `../config/${this.config.configFile}`)
    if (!fs.existsSync(configPath)) {
      console.warn(`⚠️  Configuration file not found: ${this.config.configFile}`)
    }
    
    console.log('✅ Pre-deployment checks passed')
  }
  
  async optimizeAssets() {
    console.log('⚡ Optimizing assets for CDN...')
    
    if (CONFIG.optimization.gzip) {
      await this.compressAssets('gzip')
    }
    
    if (CONFIG.optimization.brotli) {
      await this.compressAssets('brotli')
    }
    
    if (CONFIG.optimization.imageOptimization) {
      await this.optimizeImages()
    }
    
    await this.generateManifest()
    await this.setCacheHeaders()
    
    console.log('✅ Asset optimization completed')
  }
  
  async compressAssets(format) {
    console.log(`📦 Compressing assets with ${format}...`)
    
    const extensions = ['.js', '.css', '.html', '.json', '.svg']
    const files = this.getFilesByExtensions(CONFIG.distDir, extensions)
    
    for (const file of files) {
      try {
        if (format === 'gzip') {
          execSync(`gzip -9 -k "${file}"`, { stdio: 'ignore' })
        } else if (format === 'brotli') {
          execSync(`brotli -q 11 -k "${file}"`, { stdio: 'ignore' })
        }
      } catch (error) {
        console.warn(`⚠️  Failed to compress ${file}: ${error.message}`)
      }
    }
  }
  
  async optimizeImages() {
    console.log('🖼️  Optimizing images...')
    
    const imageExtensions = ['.jpg', '.jpeg', '.png', '.webp', '.svg']
    const images = this.getFilesByExtensions(CONFIG.distDir, imageExtensions)
    
    for (const image of images) {
      try {
        const ext = path.extname(image).toLowerCase()
        
        if (['.jpg', '.jpeg'].includes(ext)) {
          execSync(`jpegoptim --max=85 --strip-all "${image}"`, { stdio: 'ignore' })
        } else if (ext === '.png') {
          execSync(`optipng -o7 "${image}"`, { stdio: 'ignore' })
        } else if (ext === '.svg') {
          execSync(`svgo "${image}"`, { stdio: 'ignore' })
        }
      } catch (error) {
        console.warn(`⚠️  Failed to optimize ${image}: ${error.message}`)
      }
    }
  }
  
  async generateManifest() {
    console.log('📋 Generating asset manifest...')
    
    const manifest = {
      timestamp: new Date().toISOString(),
      version: process.env.npm_package_version || '1.0.0',
      assets: {},
      totalSize: 0
    }
    
    const files = this.getAllFiles(CONFIG.distDir)
    
    for (const file of files) {
      const relativePath = path.relative(CONFIG.distDir, file)
      const stats = fs.statSync(file)
      
      manifest.assets[relativePath] = {
        size: stats.size,
        modified: stats.mtime.toISOString(),
        hash: this.getFileHash(file)
      }
      
      manifest.totalSize += stats.size
    }
    
    const manifestPath = path.join(CONFIG.distDir, 'manifest.json')
    fs.writeFileSync(manifestPath, JSON.stringify(manifest, null, 2))
    
    console.log(`📊 Generated manifest with ${Object.keys(manifest.assets).length} assets (${this.formatBytes(manifest.totalSize)})`)
  }
  
  async setCacheHeaders() {
    console.log('🏷️  Setting cache headers...')
    
    const headersFile = path.join(CONFIG.distDir, '_headers')
    const headers = []
    
    // Generate headers for different file types
    Object.entries(CONFIG.cacheHeaders).forEach(([type, cacheControl]) => {
      if (type === 'text/html') {
        headers.push('/*')
        headers.push(`  Cache-Control: ${cacheControl}`)
        headers.push('')
      } else if (type === 'text/css') {
        headers.push('/css/*')
        headers.push(`  Cache-Control: ${cacheControl}`)
        headers.push('')
      } else if (type === 'application/javascript') {
        headers.push('/js/*')
        headers.push(`  Cache-Control: ${cacheControl}`)
        headers.push('')
      } else if (type === 'image/*') {
        headers.push('/images/*')
        headers.push(`  Cache-Control: ${cacheControl}`)
        headers.push('')
      } else if (type === 'font/*') {
        headers.push('/fonts/*')
        headers.push(`  Cache-Control: ${cacheControl}`)
        headers.push('  Access-Control-Allow-Origin: *')
        headers.push('')
      }
    })
    
    fs.writeFileSync(headersFile, headers.join('\n'))
  }
  
  async deployToProvider() {
    console.log(`🌐 Deploying to ${this.config.name}...`)
    
    switch (this.provider) {
      case 'cloudflare':
        await this.deployToCloudflare()
        break
      case 'aws':
        await this.deployToAWS()
        break
      case 'azure':
        await this.deployToAzure()
        break
      case 'gcp':
        await this.deployToGCP()
        break
      default:
        throw new Error(`Deployment method not implemented for ${this.provider}`)
    }
  }
  
  async deployToCloudflare() {
    const projectName = process.env.CLOUDFLARE_PROJECT_NAME || 'reality2-frontend'
    const command = `wrangler pages publish ${CONFIG.distDir} --project-name=${projectName}`
    
    execSync(command, { stdio: 'inherit' })
  }
  
  async deployToAWS() {
    const bucket = process.env.AWS_S3_BUCKET || 'reality2-frontend'
    const distribution = process.env.AWS_CLOUDFRONT_DISTRIBUTION_ID
    
    // Upload to S3
    execSync(`aws s3 sync ${CONFIG.distDir} s3://${bucket} --delete`, { stdio: 'inherit' })
    
    // Invalidate CloudFront cache
    if (distribution) {
      execSync(`aws cloudfront create-invalidation --distribution-id ${distribution} --paths "/*"`, { stdio: 'inherit' })
    }
  }
  
  async deployToAzure() {
    const storageAccount = process.env.AZURE_STORAGE_ACCOUNT
    const containerName = process.env.AZURE_CONTAINER_NAME || '$web'
    
    execSync(`az storage blob upload-batch --source ${CONFIG.distDir} --destination ${containerName} --account-name ${storageAccount}`, { stdio: 'inherit' })
  }
  
  async deployToGCP() {
    const bucket = process.env.GCP_STORAGE_BUCKET || 'reality2-frontend'
    
    execSync(`gsutil -m rsync -r -d ${CONFIG.distDir} gs://${bucket}`, { stdio: 'inherit' })
  }
  
  async postDeploymentVerification() {
    console.log('🔍 Running post-deployment verification...')
    
    // Add verification logic here
    // - Check if assets are accessible
    // - Verify cache headers
    // - Test CDN endpoints
    
    console.log('✅ Post-deployment verification completed')
  }
  
  // Utility methods
  getFilesByExtensions(dir, extensions) {
    const files = []
    
    const scanDir = (currentDir) => {
      const items = fs.readdirSync(currentDir)
      
      for (const item of items) {
        const fullPath = path.join(currentDir, item)
        const stat = fs.statSync(fullPath)
        
        if (stat.isDirectory()) {
          scanDir(fullPath)
        } else if (extensions.includes(path.extname(item).toLowerCase())) {
          files.push(fullPath)
        }
      }
    }
    
    scanDir(dir)
    return files
  }
  
  getAllFiles(dir) {
    const files = []
    
    const scanDir = (currentDir) => {
      const items = fs.readdirSync(currentDir)
      
      for (const item of items) {
        const fullPath = path.join(currentDir, item)
        const stat = fs.statSync(fullPath)
        
        if (stat.isDirectory()) {
          scanDir(fullPath)
        } else {
          files.push(fullPath)
        }
      }
    }
    
    scanDir(dir)
    return files
  }
  
  getFileHash(filePath) {
    const crypto = await import('crypto')
    const content = fs.readFileSync(filePath)
    return crypto.createHash('md5').update(content).digest('hex')
  }
  
  formatBytes(bytes) {
    if (bytes === 0) return '0 Bytes'
    const k = 1024
    const sizes = ['Bytes', 'KB', 'MB', 'GB']
    const i = Math.floor(Math.log(bytes) / Math.log(k))
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i]
  }
}

// CLI interface
const provider = process.argv[2] || 'cloudflare'
const deployer = new CDNDeployer(provider)

deployer.deploy().catch(error => {
  console.error('Deployment failed:', error)
  process.exit(1)
})
