@echo off
REM Reality 2.0 - Docker Management Script for Windows
REM Simplifies common Docker operations for development and production

setlocal enabledelayedexpansion

REM Configuration
set COMPOSE_DEV=docker-compose.reality.yml
set COMPOSE_PROD=docker-compose.prod.yml
set ENV_FILE=.env.docker

REM Colors (limited support in Windows)
set "GREEN=[92m"
set "YELLOW=[93m"
set "RED=[91m"
set "BLUE=[94m"
set "NC=[0m"

REM Helper functions
:print_header
echo %BLUE%🏠 Reality 2.0 - Docker Management%NC%
echo %BLUE%=================================%NC%
goto :eof

:print_success
echo %GREEN%✅ %~1%NC%
goto :eof

:print_warning
echo %YELLOW%⚠️  %~1%NC%
goto :eof

:print_error
echo %RED%❌ %~1%NC%
goto :eof

:print_info
echo %BLUE%ℹ️  %~1%NC%
goto :eof

REM Check if Docker is running
:check_docker
docker info >nul 2>&1
if errorlevel 1 (
    call :print_error "Docker is not running. Please start Docker and try again."
    exit /b 1
)
goto :eof

REM Check if compose file exists
:check_compose_file
if not exist "%~1" (
    call :print_error "Compose file %~1 not found!"
    exit /b 1
)
goto :eof

REM Development commands
:dev_start
call :print_info "Starting Reality 2.0 development environment..."
call :check_compose_file %COMPOSE_DEV%
docker-compose -f %COMPOSE_DEV% --env-file %ENV_FILE% up --build
goto :eof

:dev_start_detached
call :print_info "Starting Reality 2.0 development environment (detached)..."
call :check_compose_file %COMPOSE_DEV%
docker-compose -f %COMPOSE_DEV% --env-file %ENV_FILE% up --build -d
call :print_success "Development environment started!"
call :print_info "Frontend: http://localhost:3000"
call :print_info "Backend API: http://localhost:8000"
call :print_info "API Docs: http://localhost:8000/docs"
goto :eof

:dev_stop
call :print_info "Stopping development environment..."
docker-compose -f %COMPOSE_DEV% down
call :print_success "Development environment stopped!"
goto :eof

:dev_restart
call :print_info "Restarting development environment..."
docker-compose -f %COMPOSE_DEV% restart
call :print_success "Development environment restarted!"
goto :eof

:dev_logs
if "%~2"=="" (
    call :print_info "Showing logs for all services..."
    docker-compose -f %COMPOSE_DEV% logs -f
) else (
    call :print_info "Showing logs for %~2..."
    docker-compose -f %COMPOSE_DEV% logs -f %~2
)
goto :eof

REM Production commands
:prod_start
call :print_info "Starting Reality 2.0 production environment..."
call :check_compose_file %COMPOSE_PROD%
docker-compose -f %COMPOSE_PROD% up --build -d
call :print_success "Production environment started!"
call :print_info "Frontend: http://localhost:80"
call :print_info "Backend API: http://localhost:8000"
goto :eof

:prod_stop
call :print_info "Stopping production environment..."
docker-compose -f %COMPOSE_PROD% down
call :print_success "Production environment stopped!"
goto :eof

:prod_restart
call :print_info "Restarting production environment..."
docker-compose -f %COMPOSE_PROD% restart
call :print_success "Production environment restarted!"
goto :eof

REM Utility commands
:build_images
call :print_info "Building all Docker images..."
docker build -f Dockerfile.backend --target development -t reality2-backend:dev .
docker build -f Dockerfile.backend --target production -t reality2-backend:prod .
docker build -f Dockerfile.frontend --target development -t reality2-frontend:dev .
docker build -f Dockerfile.frontend --target production -t reality2-frontend:prod .
call :print_success "All images built successfully!"
goto :eof

:clean_up
call :print_warning "This will remove all Reality 2.0 containers, images, and volumes!"
set /p "confirm=Are you sure? (y/N): "
if /i "!confirm!"=="y" (
    call :print_info "Cleaning up Docker resources..."
    docker-compose -f %COMPOSE_DEV% down -v --rmi all 2>nul
    docker-compose -f %COMPOSE_PROD% down -v --rmi all 2>nul
    docker system prune -f
    call :print_success "Cleanup completed!"
) else (
    call :print_info "Cleanup cancelled."
)
goto :eof

:status
call :print_info "Docker container status:"
docker ps -a --filter "name=reality" --format "table {{.Names}}\t{{.Status}}\t{{.Ports}}"
echo.
call :print_info "Docker images:"
docker images --filter "reference=reality2*" --format "table {{.Repository}}\t{{.Tag}}\t{{.Size}}"
goto :eof

:shell
set service=%~2
if "%service%"=="" set service=backend
call :print_info "Opening shell in %service% container..."
if "%service%"=="backend" (
    docker-compose -f %COMPOSE_DEV% exec backend bash
) else if "%service%"=="frontend" (
    docker-compose -f %COMPOSE_DEV% exec frontend sh
) else (
    call :print_error "Unknown service: %service%. Use 'backend' or 'frontend'."
    exit /b 1
)
goto :eof

REM Help function
:show_help
call :print_header
echo.
echo Usage: %~nx0 [COMMAND]
echo.
echo Development Commands:
echo   dev-start       Start development environment (interactive)
echo   dev-start-bg    Start development environment (detached)
echo   dev-stop        Stop development environment
echo   dev-restart     Restart development environment
echo   dev-logs [svc]  Show logs (optionally for specific service)
echo.
echo Production Commands:
echo   prod-start      Start production environment
echo   prod-stop       Stop production environment
echo   prod-restart    Restart production environment
echo.
echo Utility Commands:
echo   build           Build all Docker images
echo   status          Show container and image status
echo   shell [svc]     Open shell in container (backend/frontend)
echo   clean           Clean up all Docker resources
echo   help            Show this help message
echo.
echo Examples:
echo   %~nx0 dev-start-bg    # Start development environment in background
echo   %~nx0 dev-logs backend # Show backend logs
echo   %~nx0 shell frontend   # Open shell in frontend container
echo   %~nx0 status          # Show current status
goto :eof

REM Main script logic
call :check_docker

set command=%~1
if "%command%"=="" set command=help

if "%command%"=="dev-start" (
    call :dev_start
) else if "%command%"=="dev-start-bg" (
    call :dev_start_detached
) else if "%command%"=="dev-stop" (
    call :dev_stop
) else if "%command%"=="dev-restart" (
    call :dev_restart
) else if "%command%"=="dev-logs" (
    call :dev_logs %*
) else if "%command%"=="prod-start" (
    call :prod_start
) else if "%command%"=="prod-stop" (
    call :prod_stop
) else if "%command%"=="prod-restart" (
    call :prod_restart
) else if "%command%"=="build" (
    call :build_images
) else if "%command%"=="status" (
    call :status
) else if "%command%"=="shell" (
    call :shell %*
) else if "%command%"=="clean" (
    call :clean_up
) else if "%command%"=="help" (
    call :show_help
) else if "%command%"=="--help" (
    call :show_help
) else if "%command%"=="-h" (
    call :show_help
) else (
    call :print_error "Unknown command: %command%"
    echo.
    call :show_help
    exit /b 1
)

endlocal
