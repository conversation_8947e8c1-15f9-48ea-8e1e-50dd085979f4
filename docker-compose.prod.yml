version: '3.8'

services:
  # FastAPI Backend - Production
  backend:
    build:
      context: .
      dockerfile: Dockerfile.backend
      target: production
    container_name: reality-backend-prod
    environment:
      - BACKEND_HOST=0.0.0.0
      - BACKEND_PORT=8000
      - CORS_ORIGINS=${CORS_ORIGINS:-https://reality2.app,https://www.reality2.app}
      - APP_ENVIRONMENT=production
      - MAPY_CZ_API_KEY=${MAPY_CZ_API_KEY}
    ports:
      - "8000:8000"
    volumes:
      - ./backend/logs:/app/logs
      - ./backend/uploads:/app/uploads
    networks:
      - reality-network
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8000/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s

  # Vue.js Frontend - Production with Nginx
  frontend:
    build:
      context: .
      dockerfile: Dockerfile.frontend
      target: production
    container_name: reality-frontend-prod
    ports:
      - "80:80"
    depends_on:
      - backend
    networks:
      - reality-network
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:80"]
      interval: 30s
      timeout: 10s
      retries: 3

networks:
  reality-network:
    driver: bridge
    name: reality-network-prod
