# Reality 2.0 - Docker Environment Configuration
# Environment variables for Docker containerization

# Application Environment
APP_ENVIRONMENT=development
APP_NAME=Reality 2.0

# Backend Configuration
BACKEND_HOST=0.0.0.0
BACKEND_PORT=8000

# Frontend Configuration
VITE_API_BASE_URL=http://localhost:8000
VITE_APP_NAME=Reality 2.0
VITE_APP_ENVIRONMENT=development

# CORS Configuration
CORS_ORIGINS=http://localhost:3000,http://127.0.0.1:3000

# External APIs
MAPY_CZ_API_KEY=8Rtq998py5jq8tJQ-HjbX17FiM7AnjwQxQzljSHmWJE

# Docker Configuration
COMPOSE_PROJECT_NAME=reality2

# Development Tools
ENABLE_DEBUG=true
LOG_LEVEL=INFO
