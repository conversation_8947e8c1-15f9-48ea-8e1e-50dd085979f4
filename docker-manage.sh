#!/bin/bash

# Reality 2.0 - Docker Management Script
# Simplifies common Docker operations for development and production

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Configuration
COMPOSE_DEV="docker-compose.reality.yml"
COMPOSE_PROD="docker-compose.prod.yml"
ENV_FILE=".env.docker"

# Helper functions
print_header() {
    echo -e "${BLUE}🏠 Reality 2.0 - Docker Management${NC}"
    echo -e "${BLUE}=================================${NC}"
}

print_success() {
    echo -e "${GREEN}✅ $1${NC}"
}

print_warning() {
    echo -e "${YELLOW}⚠️  $1${NC}"
}

print_error() {
    echo -e "${RED}❌ $1${NC}"
}

print_info() {
    echo -e "${BLUE}ℹ️  $1${NC}"
}

# Check if Docker is running
check_docker() {
    if ! docker info > /dev/null 2>&1; then
        print_error "Docker is not running. Please start Docker and try again."
        exit 1
    fi
}

# Check if compose file exists
check_compose_file() {
    local file=$1
    if [ ! -f "$file" ]; then
        print_error "Compose file $file not found!"
        exit 1
    fi
}

# Development commands
dev_start() {
    print_info "Starting Reality 2.0 development environment..."
    check_compose_file $COMPOSE_DEV
    docker-compose -f $COMPOSE_DEV --env-file $ENV_FILE up --build
}

dev_start_detached() {
    print_info "Starting Reality 2.0 development environment (detached)..."
    check_compose_file $COMPOSE_DEV
    docker-compose -f $COMPOSE_DEV --env-file $ENV_FILE up --build -d
    print_success "Development environment started!"
    print_info "Frontend: http://localhost:3000"
    print_info "Backend API: http://localhost:8000"
    print_info "API Docs: http://localhost:8000/docs"
}

dev_stop() {
    print_info "Stopping development environment..."
    docker-compose -f $COMPOSE_DEV down
    print_success "Development environment stopped!"
}

dev_restart() {
    print_info "Restarting development environment..."
    docker-compose -f $COMPOSE_DEV restart
    print_success "Development environment restarted!"
}

dev_logs() {
    local service=${1:-""}
    if [ -n "$service" ]; then
        print_info "Showing logs for $service..."
        docker-compose -f $COMPOSE_DEV logs -f $service
    else
        print_info "Showing logs for all services..."
        docker-compose -f $COMPOSE_DEV logs -f
    fi
}

# Production commands
prod_start() {
    print_info "Starting Reality 2.0 production environment..."
    check_compose_file $COMPOSE_PROD
    docker-compose -f $COMPOSE_PROD up --build -d
    print_success "Production environment started!"
    print_info "Frontend: http://localhost:80"
    print_info "Backend API: http://localhost:8000"
}

prod_stop() {
    print_info "Stopping production environment..."
    docker-compose -f $COMPOSE_PROD down
    print_success "Production environment stopped!"
}

prod_restart() {
    print_info "Restarting production environment..."
    docker-compose -f $COMPOSE_PROD restart
    print_success "Production environment restarted!"
}

# Utility commands
build_images() {
    print_info "Building all Docker images..."
    docker build -f Dockerfile.backend --target development -t reality2-backend:dev .
    docker build -f Dockerfile.backend --target production -t reality2-backend:prod .
    docker build -f Dockerfile.frontend --target development -t reality2-frontend:dev .
    docker build -f Dockerfile.frontend --target production -t reality2-frontend:prod .
    print_success "All images built successfully!"
}

clean_up() {
    print_warning "This will remove all Reality 2.0 containers, images, and volumes!"
    read -p "Are you sure? (y/N): " -n 1 -r
    echo
    if [[ $REPLY =~ ^[Yy]$ ]]; then
        print_info "Cleaning up Docker resources..."
        docker-compose -f $COMPOSE_DEV down -v --rmi all 2>/dev/null || true
        docker-compose -f $COMPOSE_PROD down -v --rmi all 2>/dev/null || true
        docker system prune -f
        print_success "Cleanup completed!"
    else
        print_info "Cleanup cancelled."
    fi
}

status() {
    print_info "Docker container status:"
    docker ps -a --filter "name=reality" --format "table {{.Names}}\t{{.Status}}\t{{.Ports}}"
    
    print_info "\nDocker images:"
    docker images --filter "reference=reality2*" --format "table {{.Repository}}\t{{.Tag}}\t{{.Size}}"
}

shell() {
    local service=${1:-"backend"}
    print_info "Opening shell in $service container..."
    if [ "$service" = "backend" ]; then
        docker-compose -f $COMPOSE_DEV exec backend bash
    elif [ "$service" = "frontend" ]; then
        docker-compose -f $COMPOSE_DEV exec frontend sh
    else
        print_error "Unknown service: $service. Use 'backend' or 'frontend'."
        exit 1
    fi
}

# Help function
show_help() {
    print_header
    echo
    echo "Usage: $0 [COMMAND]"
    echo
    echo "Development Commands:"
    echo "  dev-start       Start development environment (interactive)"
    echo "  dev-start-bg    Start development environment (detached)"
    echo "  dev-stop        Stop development environment"
    echo "  dev-restart     Restart development environment"
    echo "  dev-logs [svc]  Show logs (optionally for specific service)"
    echo
    echo "Production Commands:"
    echo "  prod-start      Start production environment"
    echo "  prod-stop       Stop production environment"
    echo "  prod-restart    Restart production environment"
    echo
    echo "Utility Commands:"
    echo "  build           Build all Docker images"
    echo "  status          Show container and image status"
    echo "  shell [svc]     Open shell in container (backend/frontend)"
    echo "  clean           Clean up all Docker resources"
    echo "  help            Show this help message"
    echo
    echo "Examples:"
    echo "  $0 dev-start-bg    # Start development environment in background"
    echo "  $0 dev-logs backend # Show backend logs"
    echo "  $0 shell frontend   # Open shell in frontend container"
    echo "  $0 status          # Show current status"
}

# Main script logic
main() {
    check_docker
    
    case "${1:-help}" in
        "dev-start")
            dev_start
            ;;
        "dev-start-bg")
            dev_start_detached
            ;;
        "dev-stop")
            dev_stop
            ;;
        "dev-restart")
            dev_restart
            ;;
        "dev-logs")
            dev_logs $2
            ;;
        "prod-start")
            prod_start
            ;;
        "prod-stop")
            prod_stop
            ;;
        "prod-restart")
            prod_restart
            ;;
        "build")
            build_images
            ;;
        "status")
            status
            ;;
        "shell")
            shell $2
            ;;
        "clean")
            clean_up
            ;;
        "help"|"--help"|"-h")
            show_help
            ;;
        *)
            print_error "Unknown command: $1"
            echo
            show_help
            exit 1
            ;;
    esac
}

# Run main function with all arguments
main "$@"
