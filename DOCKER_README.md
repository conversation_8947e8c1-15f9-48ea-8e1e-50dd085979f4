# Reality 2.0 - Docker Containerization Guide

## Overview

This guide covers the complete containerization setup for Reality 2.0, including separate Dockerfiles for the FastAPI backend and Vue.js frontend, plus docker-compose configurations for different environments.

## 🏗️ Architecture

```
Reality 2.0 Containerization
├── Backend (FastAPI + Python 3.11)
│   ├── Development: Hot reload, debugging tools
│   └── Production: Multi-worker, optimized
├── Frontend (Vue.js + Nginx)
│   ├── Development: Vite dev server
│   └── Production: Static files served by Nginx
└── Network: Internal Docker network for service communication
```

## 📁 File Structure

```
Reality-2.0/
├── Dockerfile.backend          # Multi-stage backend Dockerfile
├── Dockerfile.frontend         # Multi-stage frontend Dockerfile
├── docker-compose.reality.yml  # Development environment
├── docker-compose.prod.yml     # Production environment
├── .dockerignore              # Docker build exclusions
├── .env.docker               # Docker environment variables
└── DOCKER_README.md          # This guide
```

## 🚀 Quick Start

### Prerequisites
- Docker Engine 20.10+
- Docker Compose 2.0+
- Git

### Development Environment

1. **Clone and setup:**
```bash
git clone <repository-url>
cd Reality-2.0
cp .env.docker .env
```

2. **Start development environment:**
```bash
# Using the Reality 2.0 specific compose file
docker-compose -f docker-compose.reality.yml up --build

# Or with environment file
docker-compose -f docker-compose.reality.yml --env-file .env.docker up --build
```

3. **Access the application:**
- Frontend: http://localhost:3000
- Backend API: http://localhost:8000
- API Documentation: http://localhost:8000/docs

### Production Environment

```bash
# Build and start production containers
docker-compose -f docker-compose.prod.yml up --build -d

# Access the application
# Frontend: http://localhost:80
# Backend API: http://localhost:8000
```

## 🐳 Docker Images

### Backend Image (Dockerfile.backend)

**Base:** Python 3.11-slim
**Stages:**
- `base`: Common dependencies and setup
- `development`: Development tools, hot reload
- `production`: Optimized for production, multiple workers

**Features:**
- ✅ Multi-stage build for optimization
- ✅ Non-root user for security
- ✅ Health checks
- ✅ Hot reload in development
- ✅ Multiple workers in production
- ✅ Proper logging and error handling

### Frontend Image (Dockerfile.frontend)

**Base:** Node.js 18-alpine
**Stages:**
- `base`: Node.js setup
- `development`: Vite dev server
- `builder`: Build static assets
- `production`: Nginx serving static files

**Features:**
- ✅ Multi-stage build for small production image
- ✅ Nginx configuration for Vue.js SPA
- ✅ API proxy configuration
- ✅ Health checks
- ✅ Hot reload in development
- ✅ Optimized static file serving

## ⚙️ Configuration

### Environment Variables

| Variable | Description | Default | Required |
|----------|-------------|---------|----------|
| `APP_ENVIRONMENT` | Application environment | `development` | No |
| `BACKEND_HOST` | Backend host | `0.0.0.0` | No |
| `BACKEND_PORT` | Backend port | `8000` | No |
| `VITE_API_BASE_URL` | Frontend API URL | `http://localhost:8000` | No |
| `CORS_ORIGINS` | Allowed CORS origins | `http://localhost:3000` | No |
| `MAPY_CZ_API_KEY` | Mapy.cz API key | Provided | Yes |

### Docker Compose Files

#### docker-compose.reality.yml (Development)
- Hot reload for both frontend and backend
- Volume mounts for live code changes
- Development-optimized settings
- File watching for automatic rebuilds

#### docker-compose.prod.yml (Production)
- Optimized production builds
- No volume mounts
- Multiple backend workers
- Nginx serving frontend

## 🔧 Development Workflow

### Starting Development Environment
```bash
# Start all services
docker-compose -f docker-compose.reality.yml up

# Start specific service
docker-compose -f docker-compose.reality.yml up backend
docker-compose -f docker-compose.reality.yml up frontend

# Rebuild and start
docker-compose -f docker-compose.reality.yml up --build
```

### Viewing Logs
```bash
# All services
docker-compose -f docker-compose.reality.yml logs -f

# Specific service
docker-compose -f docker-compose.reality.yml logs -f backend
docker-compose -f docker-compose.reality.yml logs -f frontend
```

### Executing Commands in Containers
```bash
# Backend shell
docker-compose -f docker-compose.reality.yml exec backend bash

# Frontend shell
docker-compose -f docker-compose.reality.yml exec frontend sh

# Run backend tests
docker-compose -f docker-compose.reality.yml exec backend python -m pytest

# Install frontend dependencies
docker-compose -f docker-compose.reality.yml exec frontend npm install
```

## 🏭 Production Deployment

### Building Production Images
```bash
# Build backend production image
docker build -f Dockerfile.backend --target production -t reality2-backend:latest .

# Build frontend production image
docker build -f Dockerfile.frontend --target production -t reality2-frontend:latest .
```

### Production Deployment
```bash
# Deploy production environment
docker-compose -f docker-compose.prod.yml up -d

# Scale backend workers
docker-compose -f docker-compose.prod.yml up -d --scale backend=3

# Update production deployment
docker-compose -f docker-compose.prod.yml pull
docker-compose -f docker-compose.prod.yml up -d
```

## 🔍 Monitoring and Health Checks

### Health Check Endpoints
- **Backend:** `GET /health`
- **Frontend:** `GET /` (Nginx status)

### Container Health Status
```bash
# Check container health
docker-compose -f docker-compose.reality.yml ps

# View health check logs
docker inspect reality-backend --format='{{json .State.Health}}'
docker inspect reality-frontend --format='{{json .State.Health}}'
```

## 🛠️ Troubleshooting

### Common Issues

1. **Port conflicts:**
```bash
# Check port usage
netstat -tulpn | grep :8000
netstat -tulpn | grep :3000

# Use different ports
BACKEND_PORT=8001 docker-compose -f docker-compose.reality.yml up
```

2. **Permission issues:**
```bash
# Fix file permissions
sudo chown -R $USER:$USER ./backend/logs
sudo chown -R $USER:$USER ./backend/uploads
```

3. **Build cache issues:**
```bash
# Clear Docker cache
docker system prune -a

# Rebuild without cache
docker-compose -f docker-compose.reality.yml build --no-cache
```

4. **Network issues:**
```bash
# Recreate network
docker-compose -f docker-compose.reality.yml down
docker network prune
docker-compose -f docker-compose.reality.yml up
```

### Debugging

```bash
# Debug backend container
docker-compose -f docker-compose.reality.yml exec backend python -c "import reality_app; print('Backend OK')"

# Debug frontend container
docker-compose -f docker-compose.reality.yml exec frontend npm run build

# Check container resources
docker stats reality-backend reality-frontend
```

## 📊 Performance Optimization

### Production Optimizations
- Multi-stage builds reduce image size
- Non-root users improve security
- Health checks ensure reliability
- Nginx serves static files efficiently
- Multiple backend workers handle load

### Development Optimizations
- Volume mounts enable hot reload
- File watching for automatic rebuilds
- Development tools included
- Faster startup times

## 🔐 Security Considerations

- ✅ Non-root users in containers
- ✅ Minimal base images (Alpine/slim)
- ✅ No sensitive data in images
- ✅ Environment variable configuration
- ✅ Network isolation
- ✅ Health checks for reliability

## 📝 Next Steps

1. **CI/CD Integration:** Set up automated builds and deployments
2. **Monitoring:** Add Prometheus/Grafana for metrics
3. **Logging:** Centralized logging with ELK stack
4. **Scaling:** Kubernetes deployment for production
5. **Security:** Add security scanning and vulnerability checks

## 🆘 Support

For issues with containerization:
1. Check the troubleshooting section above
2. Review Docker and docker-compose logs
3. Verify environment variables
4. Check network connectivity between containers

The containerization setup is designed to be production-ready while maintaining excellent developer experience for local development.
