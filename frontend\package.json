{"name": "reality-frontend", "version": "1.0.0", "description": "Reality 2.0 Frontend - Vue.js 3 Application", "type": "module", "scripts": {"dev": "vite", "build": "vue-tsc && vite build", "build:cdn": "VITE_CDN_ENABLED=true vue-tsc && vite build", "preview": "vite preview", "test:unit": "vitest", "test:e2e": "playwright test", "lint": "eslint . --ext .vue,.js,.jsx,.cjs,.mjs,.ts,.tsx,.cts,.mts --fix --ignore-path .gitignore", "format": "prettier --write src/", "type-check": "vue-tsc --noEmit", "deploy:cdn": "node scripts/deploy-cdn.js", "deploy:cloudflare": "node scripts/deploy-cdn.js cloudflare", "deploy:aws": "node scripts/deploy-cdn.js aws", "deploy:azure": "node scripts/deploy-cdn.js azure", "deploy:gcp": "node scripts/deploy-cdn.js gcp", "analyze": "vite-bundle-analyzer dist"}, "dependencies": {"vue": "^3.4.0", "vue-router": "^4.2.5", "pinia": "^2.1.7", "axios": "^1.6.2", "@vueuse/core": "^10.7.0", "@headlessui/vue": "^1.7.16", "@heroicons/vue": "^2.0.18", "lodash-es": "^4.17.21", "tailwindcss": "^3.4.0", "@tailwindcss/forms": "^0.5.7", "@tailwindcss/typography": "^0.5.10", "autoprefixer": "^10.4.16", "postcss": "^8.4.32"}, "devDependencies": {"@types/node": "^20.10.5", "@types/lodash-es": "^4.17.12", "@typescript-eslint/eslint-plugin": "^6.15.0", "@typescript-eslint/parser": "^6.15.0", "@vitejs/plugin-vue": "^4.5.2", "@vue/eslint-config-prettier": "^8.0.0", "@vue/eslint-config-typescript": "^12.0.0", "@vue/test-utils": "^2.4.3", "@vue/tsconfig": "^0.5.1", "eslint": "^8.56.0", "eslint-plugin-vue": "^9.19.2", "jsdom": "^23.0.1", "prettier": "^3.1.1", "typescript": "~5.3.3", "vite": "^5.0.10", "vitest": "^1.1.0", "vue-tsc": "^1.8.25", "@playwright/test": "^1.40.1"}, "engines": {"node": ">=18.0.0", "npm": ">=8.0.0"}, "browserslist": ["> 1%", "last 2 versions", "not dead", "not ie 11"]}