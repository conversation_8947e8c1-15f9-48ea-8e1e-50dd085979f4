/**
 * CDN Configuration for Reality 2.0 Frontend
 * Manages CDN URLs and asset optimization for global delivery
 */

export interface CDNConfig {
  enabled: boolean
  baseUrl: string
  domains: {
    static: string
    images: string
    fonts: string
    videos: string
  }
  providers: {
    primary: CDNProvider
    fallback: CDNProvider
  }
  optimization: {
    imageFormats: string[]
    compressionLevels: Record<string, number>
    cacheHeaders: Record<string, string>
  }
}

export interface CDNProvider {
  name: string
  baseUrl: string
  regions: string[]
  features: string[]
}

// CDN Providers Configuration
export const CDN_PROVIDERS: Record<string, CDNProvider> = {
  cloudflare: {
    name: 'Cloudflare',
    baseUrl: 'https://cdn.reality2.app',
    regions: ['global'],
    features: ['image-optimization', 'compression', 'caching', 'ddos-protection']
  },
  
  aws_cloudfront: {
    name: 'AWS CloudFront',
    baseUrl: 'https://d1234567890.cloudfront.net',
    regions: ['us-east-1', 'eu-west-1', 'ap-southeast-1'],
    features: ['edge-locations', 'lambda-edge', 'compression', 'ssl']
  },
  
  azure_cdn: {
    name: 'Azure CDN',
    baseUrl: 'https://reality2.azureedge.net',
    regions: ['global'],
    features: ['compression', 'caching', 'ssl', 'waf']
  },
  
  google_cloud_cdn: {
    name: 'Google Cloud CDN',
    baseUrl: 'https://cdn-reality2.googleusercontent.com',
    regions: ['global'],
    features: ['edge-caching', 'compression', 'ssl', 'load-balancing']
  },
  
  jsdelivr: {
    name: 'jsDelivr',
    baseUrl: 'https://cdn.jsdelivr.net/gh/reality2-app/assets',
    regions: ['global'],
    features: ['npm-packages', 'github-releases', 'compression']
  },
  
  unpkg: {
    name: 'unpkg',
    baseUrl: 'https://unpkg.com/@reality2/assets',
    regions: ['global'],
    features: ['npm-packages', 'compression', 'auto-minification']
  }
}

// Environment-based CDN configuration
const getEnvironmentConfig = (): Partial<CDNConfig> => {
  const env = import.meta.env.MODE
  const cdnUrl = import.meta.env.VITE_CDN_URL || ''
  const cdnEnabled = import.meta.env.VITE_CDN_ENABLED === 'true'
  
  switch (env) {
    case 'production':
      return {
        enabled: cdnEnabled && !!cdnUrl,
        baseUrl: cdnUrl || CDN_PROVIDERS.cloudflare.baseUrl,
        providers: {
          primary: CDN_PROVIDERS.cloudflare,
          fallback: CDN_PROVIDERS.jsdelivr
        }
      }
      
    case 'staging':
      return {
        enabled: cdnEnabled && !!cdnUrl,
        baseUrl: cdnUrl || 'https://staging-cdn.reality2.app',
        providers: {
          primary: CDN_PROVIDERS.aws_cloudfront,
          fallback: CDN_PROVIDERS.unpkg
        }
      }
      
    case 'development':
    default:
      return {
        enabled: false,
        baseUrl: '',
        providers: {
          primary: CDN_PROVIDERS.jsdelivr,
          fallback: CDN_PROVIDERS.unpkg
        }
      }
  }
}

// Default CDN Configuration
export const CDN_CONFIG: CDNConfig = {
  enabled: false,
  baseUrl: '',
  
  domains: {
    static: import.meta.env.VITE_CDN_STATIC_URL || '',
    images: import.meta.env.VITE_CDN_IMAGES_URL || '',
    fonts: import.meta.env.VITE_CDN_FONTS_URL || '',
    videos: import.meta.env.VITE_CDN_VIDEOS_URL || ''
  },
  
  providers: {
    primary: CDN_PROVIDERS.cloudflare,
    fallback: CDN_PROVIDERS.jsdelivr
  },
  
  optimization: {
    imageFormats: ['webp', 'avif', 'jpg', 'png'],
    compressionLevels: {
      'text/css': 9,
      'text/javascript': 9,
      'application/javascript': 9,
      'text/html': 8,
      'application/json': 9,
      'image/svg+xml': 9
    },
    cacheHeaders: {
      'Cache-Control': 'public, max-age=31536000, immutable',
      'Expires': new Date(Date.now() + 365 * 24 * 60 * 60 * 1000).toUTCString(),
      'ETag': 'strong',
      'Vary': 'Accept-Encoding'
    }
  },
  
  // Merge environment-specific configuration
  ...getEnvironmentConfig()
}

// CDN URL builders
export const buildCdnUrl = (path: string, domain?: keyof CDNConfig['domains']): string => {
  if (!CDN_CONFIG.enabled) {
    return path.startsWith('/') ? path : `/${path}`
  }
  
  const baseUrl = domain && CDN_CONFIG.domains[domain] 
    ? CDN_CONFIG.domains[domain]
    : CDN_CONFIG.baseUrl
    
  const cleanPath = path.startsWith('/') ? path.slice(1) : path
  const cleanBaseUrl = baseUrl.endsWith('/') ? baseUrl.slice(0, -1) : baseUrl
  
  return `${cleanBaseUrl}/${cleanPath}`
}

export const buildImageUrl = (path: string, options?: {
  width?: number
  height?: number
  format?: string
  quality?: number
}): string => {
  const baseUrl = buildCdnUrl(path, 'images')
  
  if (!options || !CDN_CONFIG.enabled) {
    return baseUrl
  }
  
  const params = new URLSearchParams()
  if (options.width) params.set('w', options.width.toString())
  if (options.height) params.set('h', options.height.toString())
  if (options.format) params.set('f', options.format)
  if (options.quality) params.set('q', options.quality.toString())
  
  const queryString = params.toString()
  return queryString ? `${baseUrl}?${queryString}` : baseUrl
}

export const buildFontUrl = (path: string): string => {
  return buildCdnUrl(path, 'fonts')
}

export const buildStaticUrl = (path: string): string => {
  return buildCdnUrl(path, 'static')
}

// Preload critical resources
export const preloadCriticalAssets = (): void => {
  if (!CDN_CONFIG.enabled) return
  
  const criticalAssets = [
    // Critical CSS
    buildStaticUrl('css/critical.css'),
    // Critical fonts
    buildFontUrl('fonts/inter-var.woff2'),
    // Critical images
    buildImageUrl('images/logo.svg'),
  ]
  
  criticalAssets.forEach(url => {
    const link = document.createElement('link')
    link.rel = 'preload'
    link.href = url
    
    if (url.includes('.css')) link.as = 'style'
    else if (url.includes('.woff')) link.as = 'font'
    else if (url.includes('.js')) link.as = 'script'
    else if (url.match(/\.(jpg|jpeg|png|webp|avif|svg)$/)) link.as = 'image'
    
    if (link.as === 'font') {
      link.crossOrigin = 'anonymous'
    }
    
    document.head.appendChild(link)
  })
}

// CDN health check
export const checkCdnHealth = async (): Promise<boolean> => {
  if (!CDN_CONFIG.enabled) return true
  
  try {
    const healthCheckUrl = buildStaticUrl('health.json')
    const response = await fetch(healthCheckUrl, { 
      method: 'HEAD',
      cache: 'no-cache'
    })
    return response.ok
  } catch (error) {
    console.warn('CDN health check failed:', error)
    return false
  }
}

export default CDN_CONFIG
