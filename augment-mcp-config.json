{"mcpServers": {"memory": {"name": "Memory Server", "description": "MCP Memory Server for persistent memory management in Reality 2.0", "command": "npx", "args": ["-y", "@modelcontextprotocol/server-memory"], "cwd": "C:\\Users\\<USER>\\Documents\\augment-projects\\Reality-2.0", "env": {"MEMORY_STORAGE_PATH": "C:\\Users\\<USER>\\Documents\\augment-projects\\Reality-2.0\\mcp-memory-data"}, "capabilities": ["memory_store", "memory_retrieve", "memory_search", "memory_delete"], "autoStart": true, "restartOnFailure": true, "timeout": 30000}, "filesystem": {"name": "Filesystem Server", "description": "MCP Filesystem Server for file operations in Reality 2.0", "command": "npx", "args": ["@modelcontextprotocol/server-filesystem", "C:\\Users\\<USER>\\Documents\\augment-projects"], "cwd": "C:\\Users\\<USER>\\Documents\\augment-projects\\Reality-2.0", "capabilities": ["file_read", "file_write", "directory_list", "file_search"], "autoStart": true, "restartOnFailure": true, "timeout": 30000}}, "settings": {"enableMcp": true, "mcpTimeout": 30000, "mcpRetries": 3, "logLevel": "info", "logFile": "C:\\Users\\<USER>\\Documents\\augment-projects\\Reality-2.0\\mcp.log"}, "reality2": {"project": {"name": "Reality 2.0", "description": "Czech building/real estate search application", "technologies": ["FastAPI", "Vue.js", "<PERSON>er", "Mapy.cz API", "RUIAN API"], "structure": {"backend": "FastAPI backend with comprehensive error handling", "frontend": "Vue.js frontend with Mapy.cz integration", "containerization": "Docker with separate containers for backend/frontend"}}, "apis": {"mapy_cz": {"key": "8Rtq998py5jq8tJQ-HjbX17FiM7AnjwQxQzljSHmWJE", "endpoints": {"autocomplete": "/api/mapy-autocomplete", "geocode": "https://api.mapy.cz/v1/geocode"}}, "ruian": {"endpoint": "https://services.cuzk.cz/wfs/inspire-bu-wfs.asp", "format": "WFS 2.0.0"}}, "development": {"environment": "Docker containers with hot reload", "backend_port": 8000, "frontend_port": 3000, "error_handling": "Comprehensive with custom exceptions and correlation IDs"}}}